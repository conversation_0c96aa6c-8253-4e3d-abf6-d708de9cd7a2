# Guide de Diagnostic - Extension Coder Companion

## 🔍 Problème Identifié

**Symptôme** : Les onglets de la sidebar affichent du contenu dans la page de debug mais sont vides dans l'extension Chrome.

**Cause probable** : Problème de timing et de contexte d'exécution du script de la sidebar.

## 🛠️ Corrections Apportées

### 1. Chargement Manuel du Script
- **Problème** : Le script de la sidebar s'exécutait avant que le DOM soit prêt
- **Solution** : Suppression du script du HTML et chargement manuel après injection

### 2. Système de Stockage Intégré
- **Problème** : Le module `storage.js` n'était pas accessible dans le contexte de l'extension
- **Solution** : Intégration du système de stockage directement dans le script de la sidebar

### 3. Logs de Debug Améliorés
- **Ajout** : Logs détaillés pour tracer l'exécution et identifier les problèmes

## 📋 Étapes de Diagnostic

### Étape 1 : Vérifier le Chargement de l'Extension
1. Allez dans `chrome://extensions/`
2. Vérifiez que "Coder Companion" est activé
3. Rechargez l'extension (bouton 🔄)
4. Vérifiez qu'il n'y a pas d'erreurs dans la console

### Étape 2 : Tester sur Google AI Studio
1. Allez sur `https://aistudio.google.com/`
2. Ouvrez la console développeur (F12)
3. Cliquez sur le bouton de l'extension ou utilisez `Ctrl+Shift+C`
4. Vérifiez les logs dans la console

### Étape 3 : Analyser les Logs
Recherchez ces messages dans la console :
```
[Coder Companion] Initializing extension...
[Coder Companion] Sidebar HTML loaded
[Coder Companion] Sidebar script loaded
[Sidebar] Initializing sidebar manager...
[Sidebar] Loading modules...
[Sidebar] Loading panel components...
[Sidebar] All panels loaded
```

### Étape 4 : Vérifier les Éléments DOM
Dans la console, exécutez :
```javascript
// Vérifier que la sidebar existe
document.getElementById('cc-sidebar')

// Vérifier les onglets
document.getElementById('projects-tab')
document.getElementById('personas-tab')

// Vérifier le gestionnaire de sidebar
window.sidebarManager
```

## 🧪 Pages de Test Disponibles

### 1. Test Extension Context
```
file:///Users/<USER>/projects/gemini-extension/extension/test-extension-context.html
```
- Simule exactement l'environnement de l'extension
- Boutons de test pour diagnostiquer les problèmes
- Logs en temps réel

### 2. Debug Sidebar
```
file:///Users/<USER>/projects/gemini-extension/extension/debug-sidebar.html
```
- Test standalone avec logs de debug
- Fonctionne indépendamment de l'extension

### 3. Test Sidebar Simple
```
file:///Users/<USER>/projects/gemini-extension/extension/test-sidebar.html
```
- Test basique des fonctionnalités

## 🔧 Solutions de Dépannage

### Si les Panels sont Vides
1. **Vérifiez le timing** : Le script s'exécute-t-il après l'injection du HTML ?
2. **Vérifiez les éléments DOM** : Les éléments `projects-tab`, etc. existent-ils ?
3. **Vérifiez les logs** : Y a-t-il des erreurs dans `createProjectsPanel()` ?

### Si le Stockage ne Fonctionne Pas
1. **Vérifiez localStorage** : Ouvrez les DevTools > Application > Local Storage
2. **Testez manuellement** : `localStorage.setItem('test', 'value')`
3. **Vérifiez les permissions** : L'extension a-t-elle accès au stockage ?

### Si les Boutons ne Répondent Pas
1. **Vérifiez les event listeners** : Sont-ils attachés après la création des panels ?
2. **Vérifiez les erreurs JavaScript** : Y a-t-il des erreurs qui cassent l'exécution ?
3. **Testez manuellement** : Cliquez sur les boutons dans la console

## 📊 Commandes de Debug Utiles

### Dans la Console de l'Extension
```javascript
// Vérifier l'état de la sidebar
window.sidebarManager?.panelsLoaded

// Forcer le rechargement des panels
window.sidebarManager?.loadPanels()

// Tester le stockage
window.sidebarManager?.storage.set('test', {hello: 'world'})

// Créer un projet de test
window.sidebarManager?.projectManager.createProject({
  name: 'Test Project',
  description: 'Debug test'
})
```

### Vérifier le Contenu des Panels
```javascript
// Vérifier le contenu HTML des panels
console.log('Projects:', document.getElementById('projects-tab')?.innerHTML?.length);
console.log('Personas:', document.getElementById('personas-tab')?.innerHTML?.length);
```

## 🎯 Prochaines Actions

Si le problème persiste :
1. **Vérifiez les permissions** dans le manifest
2. **Testez avec une extension minimale** pour isoler le problème
3. **Vérifiez la compatibilité** avec la version de Chrome
4. **Analysez les erreurs** dans la console de l'extension

## 📞 Support

Si vous continuez à avoir des problèmes :
1. Partagez les logs de la console
2. Indiquez la version de Chrome utilisée
3. Précisez les étapes exactes qui ne fonctionnent pas
