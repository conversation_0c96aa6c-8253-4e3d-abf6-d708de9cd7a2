#!/bin/bash

# Script pour recharger l'extension Chrome
echo "🔄 Rechargement de l'extension Coder Companion..."

# Ouvrir Chrome avec l'extension rechargée
osascript -e 'tell application "Google Chrome"
    activate
    open location "chrome://extensions/"
end tell'

echo "✅ Chrome ouvert sur la page des extensions"
echo "📝 Instructions:"
echo "   1. <PERSON><PERSON><PERSON> 'Coder Companion' dans la liste"
echo "   2. <PERSON><PERSON>z sur le bouton de rechargement 🔄"
echo "   3. <PERSON>ez sur https://aistudio.google.com/"
echo "   4. Cliquez sur le bouton de l'extension ou utilisez Ctrl+Shift+C"

# Optionnel: ouvrir aussi AI Studio
read -p "Voulez-vous ouvrir Google AI Studio maintenant? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    osascript -e 'tell application "Google Chrome"
        open location "https://aistudio.google.com/"
    end tell'
    echo "🚀 Google AI Studio ouvert"
fi

echo "🎯 Test de la sidebar:"
echo "   - <PERSON><PERSON><PERSON> sur Projects, Personas, Artifacts, Export"
echo "   - Vérifiez que les données se chargent"
echo "   - Testez les boutons New/Refresh"
echo "   - C<PERSON>z sur les éléments de liste"
