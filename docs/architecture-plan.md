# Chrome Extension Architecture Plan
## Persona-Driven Development Workflow for Google AI Studio

### Project Overview
A Chrome extension that enhances Google AI Studio to serve as a "coder companion" for the Persona-Driven Development Workflow. The extension operates entirely client-side with localStorage persistence.

### Core Modules Required

#### 1. Project Manager
- **Purpose**: Create, duplicate, archive, and manage project metadata
- **Key Features**:
  - Project creation with metadata (name, description, personas)
  - Project duplication and archiving
  - Project list view with search/filter
  - Slug generation (kebab-case) for export

#### 2. Persona Workflow
- **Purpose**: Handle prompt processing and persona metadata management
- **Key Features**:
  - Persona definition and management
  - Prompt template system
  - Context switching between personas
  - Persona-specific conversation history

#### 3. Artifact Editor/Viewer
- **Purpose**: Split Markdown editor with live preview, version control
- **Key Features**:
  - Markdown editor with syntax highlighting
  - Live preview pane
  - Version history and diff viewer
  - Version restore functionality
  - YAML front-matter injection

#### 4. Export System
- **Purpose**: Single file and bulk ZIP export with proper naming
- **Key Features**:
  - Single Markdown file export with Blob/FileSaver
  - Bulk ZIP export with JSZip
  - Proper slug-based file naming
  - YAML metadata injection

### Data Structures (Assumptions - Need Specification)

```javascript
// Project Structure
const Project = {
  id: string,
  name: string,
  slug: string, // kebab-case
  description: string,
  createdAt: Date,
  updatedAt: Date,
  personas: Persona[],
  artifacts: Artifact[],
  metadata: object
};

// Persona Structure
const Persona = {
  id: string,
  name: string,
  slug: string,
  description: string,
  promptTemplate: string,
  context: object,
  metadata: object
};

// Artifact Structure
const Artifact = {
  id: string,
  name: string,
  slug: string,
  content: string, // Markdown
  versions: Version[],
  currentVersion: string,
  persona: string, // persona ID
  metadata: object,
  frontMatter: object // YAML front-matter
};

// Version Structure
const Version = {
  id: string,
  content: string,
  timestamp: Date,
  changes: string, // diff summary
  author: string // persona or user
};
```

### UI Components Planned

#### 1. Main Extension Panel (Sidebar/Overlay)
- Project selector dropdown
- Quick actions (new project, export, settings)
- Active persona indicator

#### 2. Project Management Interface
- Project list with search/filter
- Project creation modal
- Project settings panel

#### 3. Persona Workflow Interface
- Persona selector
- Prompt template editor
- Context management panel

#### 4. Artifact Editor Interface
- Split-pane Markdown editor
- Live preview pane
- Version history sidebar
- YAML front-matter editor

#### 5. Export Dialog
- Single file export options
- Bulk ZIP export configuration
- Preview of file structure

### Integration Strategy with Google AI Studio

#### Option 1: Sidebar Panel (Recommended)
- Inject a collapsible sidebar into the Google AI Studio interface
- Minimal interference with existing UI
- Easy access to all extension features

#### Option 2: Floating Overlay
- Draggable floating panel
- Can be minimized/maximized
- Higher flexibility but potentially intrusive

#### Option 3: Modal Integration
- Triggered by toolbar button
- Full-screen overlay when active
- Clean separation but less seamless

### Technical Implementation Plan

#### Chrome Extension Structure
```
/extension
├── manifest.json          # Extension configuration
├── content-script.js      # Google AI Studio integration
├── background.js          # Service worker
├── popup/                 # Extension popup (if needed)
├── sidebar/               # Main extension UI
│   ├── index.html
│   ├── style.css
│   └── script.js
├── modules/               # Core functionality
│   ├── project-manager.js
│   ├── persona-workflow.js
│   ├── artifact-editor.js
│   └── export-system.js
├── utils/                 # Utilities
│   ├── storage.js         # localStorage wrapper
│   ├── slug-generator.js  # Kebab-case generation
│   └── yaml-processor.js  # Front-matter handling
└── lib/                   # External dependencies
    ├── marked.js          # Markdown parsing
    ├── jszip.min.js       # ZIP export
    └── file-saver.js      # File download
```

### Next Steps Required
1. **Clarify Data Structures**: Need exact field definitions for Project, Persona, Artifact, Version
2. **Define Workflow Steps**: Specific user journey through persona-driven development
3. **UI Mockups**: Visual design for each component
4. **Integration Points**: Exact Google AI Studio DOM elements to target

### Assumptions Made
- Extension will inject UI into existing Google AI Studio page
- All data persisted in localStorage with autosave
- Export follows standard file naming conventions
- Markdown editor supports standard syntax
- YAML front-matter follows Jekyll/Hugo conventions