# Guide des Fonctionnalités d'Édition

Ce guide explique comment utiliser les nouvelles fonctionnalités d'édition implémentées dans l'extension Chrome AI Studio Coder Companion.

## Vue d'ensemble

Les fonctionnalités d'édition permettent aux utilisateurs de modifier les projets, personas et artefacts existants directement depuis la barre latérale. Chaque type d'élément dispose de sa propre interface d'édition avec validation et gestion d'erreurs.

## Fonctionnalités Implémentées

### 1. Édition des Projets

**Accès :** Cliquez sur un projet dans la liste, puis sur "Edit Project" dans la modale de détails.

**Champs modifiables :**
- Nom du projet (requis)
- Description
- Statut (Active, Draft, Completed, Archived)
- Priorité (Low, Medium, High, Urgent)
- Tags (séparés par des virgules)

**Fonctionnalités :**
- Validation en temps réel
- Sauvegarde automatique des métadonnées
- Mise à jour de la date de modification
- Incrémentation automatique de la version
- Gestion d'erreurs avec messages utilisateur

### 2. Édition des Personas

**Accès :** Cliquez sur un persona dans la liste, puis sur "Edit Persona" dans la modale de détails.

**Champs modifiables :**
- Nom du persona (requis)
- Description
- Rôle (Assistant, Developer, Designer, Analyst, Consultant, Other)
- Personnalité (Professional, Friendly, Casual, Formal, Creative, Analytical)
- Expertise (compétences séparées par des virgules)
- Prompt système

**Fonctionnalités :**
- Validation des données persona
- Mise à jour des statistiques d'utilisation
- Préservation de l'historique d'utilisation
- Interface utilisateur adaptée aux champs spécialisés

### 3. Édition des Artefacts

**Accès :** Cliquez sur un artefact dans la liste, puis sur "Edit Artifact" dans la modale de détails.

**Champs modifiables :**
- Nom de l'artefact (requis)
- Description
- Type (Document, Code, Template, Specification, Other)
- Contenu (avec support Markdown)
- Tags (séparés par des virgules)

**Fonctionnalités :**
- Création automatique de versions lors des modifications
- Comptage automatique des mots
- Support du formatage Markdown
- Historique des versions préservé
- Éditeur de contenu intégré

### 4. Création d'Artefacts

**Accès :** Cliquez sur "New Artifact" dans l'onglet Artifacts.

**Fonctionnalités :**
- Interface de création simplifiée
- Contenu initial optionnel
- Validation des données
- Intégration avec l'éditeur complet

## Architecture Technique

### Méthodes Principales

#### Projets
- `showEditProjectModal(projectId)` - Affiche la modale d'édition
- `handleEditProject(projectId, form, modal)` - Traite la soumission du formulaire
- `showProjectDetails(project)` - Affiche les détails avec bouton d'édition

#### Personas
- `showEditPersonaModal(personaId)` - Affiche la modale d'édition
- `handleEditPersona(personaId, form, modal)` - Traite la soumission du formulaire
- `showPersonaDetails(persona)` - Affiche les détails avec boutons d'action
- `activatePersona(personaId)` - Active un persona

#### Artefacts
- `showEditArtifactModal(artifactId)` - Affiche la modale d'édition
- `handleEditArtifact(artifactId, form, modal)` - Traite la soumission du formulaire
- `showCreateArtifactModal()` - Affiche la modale de création
- `handleCreateArtifact(form, modal)` - Traite la création
- `showArtifactDetails(artifact)` - Affiche les détails avec bouton d'édition

### Gestion des Erreurs

Toutes les fonctionnalités d'édition incluent :
- Validation côté client
- Gestion des erreurs serveur
- Messages d'erreur utilisateur
- États de chargement
- Restauration des boutons en cas d'erreur

### Intégration

Les fonctionnalités d'édition s'intègrent avec :
- Les modules de gestion existants (ProjectManager, PersonaWorkflow, ArtifactEditor)
- Le système de stockage localStorage
- Les notifications utilisateur
- Le système de versioning pour les artefacts

## Tests

Un script de test est disponible dans `extension/test-edit-features.js` pour vérifier :
- La présence de toutes les méthodes requises
- L'accessibilité globale via `window.sidebarApp`
- La disponibilité des managers de données
- La création de modales
- La validation des formulaires

**Utilisation :**
```javascript
// Dans la console du navigateur
testEditFeatures();
```

## Styles et Interface

Les styles CSS incluent :
- Modales responsives avec animations
- Formulaires structurés et accessibles
- Support du mode sombre
- Styles spécialisés pour les détails d'éléments
- États de chargement et d'erreur

## Prochaines Améliorations

- Édition en lot pour plusieurs éléments
- Historique des modifications avec undo/redo
- Validation avancée avec règles métier
- Synchronisation en temps réel
- Export/import des configurations d'édition
