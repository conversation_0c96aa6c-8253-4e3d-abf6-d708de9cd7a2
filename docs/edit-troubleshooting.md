# Guide de Dépannage - Fonctionnalités d'Édition

## 🚨 Problème : "Edit functionality coming soon" s'affiche encore

### Diagnostic
Si vous voyez encore l'alerte "Edit functionality coming soon" quand vous cliquez sur Edit, cela peut être dû à :

1. **Cache du navigateur** - L'ancienne version du script est en cache
2. **Extension non rechargée** - Les modifications ne sont pas prises en compte
3. **Conflit de versions** - Plusieurs versions du script coexistent

### Solutions

#### 1. Recharger l'extension
```bash
# Dans Chrome, aller à chrome://extensions/
# Cliquer sur le bouton "Recharger" de l'extension
# Ou désactiver/réactiver l'extension
```

#### 2. Vider le cache
```bash
# Ouvrir les outils de développement (F12)
# Aller dans l'onglet Network
# Cocher "Disable cache"
# Recharger la page (Ctrl+R ou Cmd+R)
```

#### 3. Vérifier les scripts chargés
```javascript
// Dans la console du navigateur
console.log('Content script version check:');
if (window.coderCompanionInjector && window.coderCompanionInjector.handleEditItem) {
  console.log('✅ New edit functionality loaded');
} else {
  console.log('❌ Old version still loaded');
}
```

#### 4. Forcer le rechargement complet
```bash
# Fermer tous les onglets avec l'extension
# Recharger l'extension dans chrome://extensions/
# Rouvrir la page et tester
```

## 🔧 Problème : La modale d'édition ne s'ouvre pas

### Diagnostic
```javascript
// Tester dans la console
testEditIntegration(); // Utilise le script de test
```

### Solutions possibles

#### 1. Vérifier que la sidebar est ouverte
- La sidebar doit être visible pour que l'édition fonctionne
- Cliquer sur l'icône de l'extension pour ouvrir la sidebar

#### 2. Vérifier les permissions
```javascript
// Dans la console
if (typeof chrome !== 'undefined' && chrome.runtime) {
  console.log('✅ Chrome runtime available');
} else {
  console.log('❌ Chrome runtime not available');
}
```

#### 3. Vérifier la communication
```javascript
// Tester l'envoi de messages
chrome.runtime.sendMessage({
  type: 'SIDEBAR_ACTION',
  action: 'edit',
  itemType: 'project',
  itemId: 'test'
}, (response) => {
  console.log('Message response:', response);
});
```

## 📱 Problème : L'édition ne fonctionne que depuis la sidebar

### Explication
C'est le comportement normal ! Les fonctionnalités d'édition sont conçues pour fonctionner de deux façons :

1. **Depuis la sidebar** - Cliquer directement sur un élément dans la liste
2. **Depuis le content script** - Cliquer sur "Edit" dans les modales de détails

### Vérification
```javascript
// Vérifier que les deux méthodes fonctionnent
// 1. Test sidebar direct
if (window.sidebarApp) {
  console.log('✅ Sidebar edit available');
}

// 2. Test content script integration
if (window.coderCompanionInjector && window.coderCompanionInjector.handleEditItem) {
  console.log('✅ Content script edit available');
}
```

## 🎯 Problème : Les données ne se sauvegardent pas

### Diagnostic
```javascript
// Vérifier le stockage
chrome.storage.local.get(null, (data) => {
  console.log('Stored data:', data);
});
```

### Solutions

#### 1. Vérifier les permissions de stockage
Dans `manifest.json`, s'assurer que :
```json
{
  "permissions": ["storage"]
}
```

#### 2. Vérifier les erreurs de validation
- Ouvrir la console pendant l'édition
- Chercher les messages d'erreur de validation
- Vérifier que tous les champs requis sont remplis

#### 3. Tester le stockage manuellement
```javascript
// Test de sauvegarde
const testData = { test: 'value', timestamp: Date.now() };
chrome.storage.local.set({ testEdit: testData }, () => {
  console.log('Test save completed');
  
  chrome.storage.local.get('testEdit', (result) => {
    console.log('Test retrieve:', result);
  });
});
```

## 🔄 Problème : L'interface ne se met pas à jour après édition

### Solutions

#### 1. Vérifier le rafraîchissement automatique
```javascript
// Dans la sidebar, après édition, vérifier :
if (window.sidebarApp && window.sidebarApp.loadTabData) {
  window.sidebarApp.loadTabData('projects'); // ou 'personas', 'artifacts'
}
```

#### 2. Forcer le rafraîchissement
```javascript
// Rafraîchir manuellement
if (window.sidebarApp) {
  window.sidebarApp.refreshData();
}
```

## 🐛 Débogage Avancé

### 1. Activer les logs détaillés
```javascript
// Dans la console, avant de tester
localStorage.setItem('cc_debug', 'true');
```

### 2. Vérifier l'état des managers
```javascript
// Vérifier que tous les managers sont initialisés
if (window.sidebarApp) {
  console.log('Project Manager:', !!window.sidebarApp.projectManager);
  console.log('Persona Manager:', !!window.sidebarApp.personaManager);
  console.log('Artifact Manager:', !!window.sidebarApp.artifactManager);
}
```

### 3. Test complet de l'édition
```javascript
// Utiliser le script de test complet
testEditFeatures(); // Test des fonctionnalités
testEditIntegration(); // Test de l'intégration
```

## 📞 Support

Si les problèmes persistent :

1. **Collecter les informations** :
   - Version du navigateur
   - Messages d'erreur dans la console
   - Étapes pour reproduire le problème

2. **Logs utiles** :
   ```javascript
   // Exporter les logs pour diagnostic
   console.log('Browser:', navigator.userAgent);
   console.log('Extension context:', typeof chrome !== 'undefined');
   console.log('Sidebar available:', typeof window.sidebarApp !== 'undefined');
   console.log('Content script available:', typeof window.coderCompanionInjector !== 'undefined');
   ```

3. **Tests de régression** :
   - Tester avec une nouvelle installation de l'extension
   - Tester sur une page vierge
   - Tester avec les outils de développement ouverts

## ✅ Checklist de Vérification

Avant de signaler un problème, vérifier :

- [ ] Extension rechargée après modifications
- [ ] Cache du navigateur vidé
- [ ] Sidebar ouverte et visible
- [ ] Aucune erreur dans la console
- [ ] Permissions d'extension accordées
- [ ] Tests de base passent (`testEditFeatures()`)
- [ ] Communication entre scripts fonctionne (`testEditIntegration()`)

---

**Note** : La plupart des problèmes d'édition sont résolus en rechargeant l'extension et en vidant le cache du navigateur.
