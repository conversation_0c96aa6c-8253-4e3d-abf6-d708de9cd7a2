# Guide de Debug des Modales d'Édition

## 🔍 Diagnostic Étape par Étape

### Étape 1 : Vérification de Base
1. **Rechargez l'extension** dans `chrome://extensions/`
2. **Videz le cache** du navigateur (F12 → Network → Disable cache)
3. **Rafraîchissez la page**
4. **Ouvrez la console** (F12 → Console)

### Étape 2 : Test de Diagnostic
Dans la console, exécutez :
```javascript
checkEditState();
```

**Résultats attendus :**
- ✅ `CoderCompanionInjector exists: true`
- ✅ `handleEditItem exists: true`
- ✅ `openEditModalDirectly exists: true`
- ✅ `showEditProjectModal exists: true`

### Étape 3 : Test de Création de Modal
Dans la console, exécutez :
```javascript
createTestModal();
```

**Si ça marche :** Vous devriez voir une modal de test s'ouvrir.
**Si ça ne marche pas :** Il y a un problème avec la méthode `createModal`.

### Étape 4 : Test de Simulation d'Édition
Dans la console, exécutez :
```javascript
simulateEditClick();
```

**Observez les logs :** Vous devriez voir :
- `Calling handleEditItem...`
- `Opening edit modal directly for project: debug-test-123`
- `Opening project edit modal...`
- `Modal check after 500ms: true` (si ça marche)

### Étape 5 : Test Manuel
1. **Cliquez sur un élément** dans la liste pour voir ses détails
2. **Cliquez sur "Edit"**
3. **Observez la console** pour les messages de debug

## 🚨 Problèmes Possibles et Solutions

### Problème 1 : CoderCompanionInjector n'existe pas
**Symptôme :** `CoderCompanionInjector exists: false`
**Solution :**
1. Vérifiez que l'extension est activée
2. Rechargez l'extension
3. Rafraîchissez la page

### Problème 2 : Les méthodes d'édition n'existent pas
**Symptôme :** `showEditProjectModal exists: false`
**Solution :**
1. Le content script n'est pas complètement chargé
2. Rechargez l'extension et la page
3. Vérifiez les erreurs JavaScript dans la console

### Problème 3 : createTestModal ne fonctionne pas
**Symptôme :** Erreur lors de la création de la modal de test
**Solution :**
1. Problème avec les styles CSS
2. Conflit avec d'autres extensions
3. Problème de CSP (Content Security Policy)

### Problème 4 : Modal créée mais invisible
**Symptôme :** `Modal check after 500ms: true` mais rien de visible
**Solutions possibles :**
1. **Z-index trop bas :** La modal est derrière d'autres éléments
2. **Styles CSS écrasés :** D'autres styles interfèrent
3. **Modal hors écran :** Positionnement incorrect

### Problème 5 : Erreur JavaScript
**Symptôme :** Erreurs rouges dans la console
**Solution :**
1. Notez l'erreur exacte
2. Vérifiez si c'est lié aux données de l'item
3. Essayez avec des données simplifiées

## 🔧 Solutions de Dépannage

### Solution 1 : Forcer la Création de Modal
```javascript
// Test direct de création de modal
if (window.coderCompanionInjector) {
  const testItem = { id: 'test', name: 'Test Item', status: 'active' };
  window.coderCompanionInjector.showEditProjectModal('test-id', testItem);
}
```

### Solution 2 : Vérifier les Styles
```javascript
// Vérifier si les styles sont ajoutés
const styles = document.getElementById('cc-modal-styles');
console.log('Modal styles present:', !!styles);
```

### Solution 3 : Vérifier le DOM
```javascript
// Après avoir tenté de créer une modal
const modal = document.querySelector('.cc-modal-overlay');
console.log('Modal in DOM:', !!modal);
if (modal) {
  console.log('Modal styles:', window.getComputedStyle(modal));
}
```

### Solution 4 : Test avec Styles Forcés
```javascript
// Créer une modal avec styles inline forcés
if (window.coderCompanionInjector) {
  const modal = document.createElement('div');
  modal.style.cssText = `
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: rgba(0,0,0,0.8) !important;
    z-index: 999999 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  `;
  modal.innerHTML = `
    <div style="background: white; padding: 20px; border-radius: 8px; max-width: 400px;">
      <h3>Test Modal</h3>
      <p>Si vous voyez ceci, les modales peuvent s'afficher !</p>
      <button onclick="this.closest('div').remove()">Fermer</button>
    </div>
  `;
  document.body.appendChild(modal);
}
```

## 📋 Checklist de Debug

### Avant de Commencer
- [ ] Extension rechargée
- [ ] Cache vidé
- [ ] Page rafraîchie
- [ ] Console ouverte

### Tests à Effectuer
- [ ] `checkEditState()` - État général
- [ ] `createTestModal()` - Test de création
- [ ] `simulateEditClick()` - Test de simulation
- [ ] Test manuel avec clic sur "Edit"

### Informations à Collecter
- [ ] Messages de la console
- [ ] Erreurs JavaScript
- [ ] État des éléments DOM
- [ ] Styles CSS appliqués

## 🎯 Résultats Attendus

Si tout fonctionne correctement :
1. ✅ Tous les tests passent
2. ✅ Modal de test s'affiche
3. ✅ Simulation d'édition fonctionne
4. ✅ Clic manuel ouvre la modal d'édition

Si ça ne fonctionne pas :
1. ❌ Identifier l'étape qui échoue
2. ❌ Noter les erreurs exactes
3. ❌ Essayer les solutions de dépannage
4. ❌ Collecter les informations pour diagnostic

---

**Important :** Utilisez le script `extension/debug-edit-simple.js` pour tous ces tests. Il est spécialement conçu pour diagnostiquer les problèmes de modales d'édition.
