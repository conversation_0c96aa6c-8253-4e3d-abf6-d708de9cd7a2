{"manifest_version": 3, "name": "Coder Companion", "version": "1.0.0", "description": "A Chrome extension that enhances Google AI Studio with persona-driven development workflows, project management, and artifact organization.", "permissions": ["storage", "activeTab", "scripting", "downloads"], "host_permissions": ["https://aistudio.google.com/*", "https://makersuite.google.com/*"], "action": {"default_popup": "popup/index.html", "default_title": "Coder Companion", "default_icon": {"16": "assets/icons/icon16.png", "48": "assets/icons/icon48.png", "128": "assets/icons/icon128.png"}}, "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["https://aistudio.google.com/*", "https://makersuite.google.com/*"], "js": ["content-script.js"], "css": ["styles/extension.css"], "run_at": "document_end"}], "icons": {"16": "assets/icons/icon16.png", "48": "assets/icons/icon48.png", "128": "assets/icons/icon128.png"}, "web_accessible_resources": [{"resources": ["sidebar/index.html", "sidebar/style.css", "sidebar/script.js", "sidebar/script-minimal.js", "sidebar/components/*", "modules/*", "utils/*", "lib/*", "styles/*", "assets/*", "test-communication.html", "test-sidebar.html", "debug-sidebar.html", "test-extension-context.html"], "matches": ["https://aistudio.google.com/*", "https://makersuite.google.com/*"]}]}