// extension/optimize.js - Performance optimizations for Coder Companion

class PerformanceOptimizer {
  constructor() {
    this.optimizations = new Map();
    this.metrics = new Map();
    this.init();
  }

  async init() {
    console.log('[Optimizer] Initializing performance optimizations...');

    // Run optimizations
    await this.optimizeStorage();
    await this.optimizeRendering();
    await this.optimizeMemory();
    await this.optimizeBundleSize();

    // Start monitoring
    this.startPerformanceMonitoring();

    console.log('[Optimizer] Optimizations applied successfully');
  }

  // Storage Optimizations
  async optimizeStorage() {
    console.log('[Optimizer] Optimizing storage...');

    // Implement storage optimizations
    const storageOptimization = {
      name: 'Storage Optimization',
      description: 'Optimizing localStorage usage and caching',
      optimizations: [
        'Implement data compression for large artifacts',
        'Add intelligent caching with LRU eviction',
        'Batch storage operations to reduce I/O',
        'Implement storage quota monitoring'
      ]
    };

    this.optimizations.set('storage', storageOptimization);

    // Apply storage optimizations
    this.optimizeStorageCompression();
    this.optimizeStorageCaching();
    this.optimizeStorageBatching();

    console.log('[Optimizer] Storage optimized');
  }

  optimizeStorageCompression() {
    // Enable compression for data over 1KB
    this.storageCompressionThreshold = 1024;

    // Implement simple compression (in production, use proper compression library)
    this.compressData = (data) => {
      if (typeof data === 'string' && data.length > this.storageCompressionThreshold) {
        // Simple compression: remove extra whitespace and compress repeated characters
        return data.replace(/\s+/g, ' ').replace(/(.)\1{2,}/g, '$1$1');
      }
      return data;
    };

    this.decompressData = (data) => {
      // Simple decompression (reverse the compression)
      return data;
    };
  }

  optimizeStorageCaching() {
    // Implement LRU cache for frequently accessed data
    this.cache = new Map();
    this.cacheMaxSize = 50;
    this.cacheAccessOrder = [];

    this.getCached = (key) => {
      if (this.cache.has(key)) {
        // Move to end (most recently used)
        this.cacheAccessOrder = this.cacheAccessOrder.filter(k => k !== key);
        this.cacheAccessOrder.push(key);
        return this.cache.get(key);
      }
      return null;
    };

    this.setCached = (key, value) => {
      if (this.cache.size >= this.cacheMaxSize) {
        // Remove least recently used item
        const lruKey = this.cacheAccessOrder.shift();
        this.cache.delete(lruKey);
      }

      this.cache.set(key, value);
      this.cacheAccessOrder.push(key);
    };
  }

  optimizeStorageBatching() {
    // Batch storage operations
    this.storageQueue = [];
    this.batchTimeout = null;

    this.batchStorageOperation = (operation) => {
      this.storageQueue.push(operation);

      if (!this.batchTimeout) {
        this.batchTimeout = setTimeout(() => {
          this.processStorageBatch();
        }, 16); // ~60fps
      }
    };

    this.processStorageBatch = () => {
      const operations = [...this.storageQueue];
      this.storageQueue = [];
      this.batchTimeout = null;

      // Process batched operations
      operations.forEach(op => {
        try {
          op();
        } catch (error) {
          console.error('[Optimizer] Batched storage operation failed:', error);
        }
      });
    };
  }

  // Rendering Optimizations
  async optimizeRendering() {
    console.log('[Optimizer] Optimizing rendering...');

    const renderingOptimization = {
      name: 'Rendering Optimization',
      description: 'Optimizing DOM manipulation and rendering performance',
      optimizations: [
        'Implement virtual scrolling for large lists',
        'Use document fragments for batch DOM updates',
        'Debounce rapid UI updates',
        'Optimize CSS animations and transitions'
      ]
    };

    this.optimizations.set('rendering', renderingOptimization);

    this.optimizeVirtualScrolling();
    this.optimizeDOMBatching();
    this.optimizeEventHandling();

    console.log('[Optimizer] Rendering optimized');
  }

  optimizeVirtualScrolling() {
    // Implement virtual scrolling for lists with many items
    this.virtualScrollThreshold = 100;

    this.createVirtualList = (container, items, itemHeight = 50) => {
      if (items.length <= this.virtualScrollThreshold) {
        return this.createRegularList(container, items);
      }

      let scrollTop = 0;
      const containerHeight = container.clientHeight;
      const visibleItems = Math.ceil(containerHeight / itemHeight);
      const bufferItems = 5; // Render 5 extra items above and below

      const renderVisibleItems = () => {
        const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - bufferItems);
        const endIndex = Math.min(items.length, startIndex + visibleItems + 2 * bufferItems);

        const fragment = document.createDocumentFragment();
        for (let i = startIndex; i < endIndex; i++) {
          const itemElement = this.createListItem(items[i]);
          itemElement.style.transform = `translateY(${i * itemHeight}px)`;
          fragment.appendChild(itemElement);
        }

        container.innerHTML = '';
        container.appendChild(fragment);
      };

      container.addEventListener('scroll', () => {
        scrollTop = container.scrollTop;
        renderVisibleItems();
      });

      renderVisibleItems();
    };
  }

  optimizeDOMBatching() {
    // Use document fragments for batch DOM updates
    this.createBatchedUpdate = (container) => {
      const fragment = document.createDocumentFragment();
      const operations = [];

      return {
        append: (element) => {
          operations.push(() => fragment.appendChild(element));
        },
        prepend: (element) => {
          operations.push(() => fragment.insertBefore(element, fragment.firstChild));
        },
        remove: (element) => {
          operations.push(() => {
            if (element.parentNode) {
              element.parentNode.removeChild(element);
            }
          });
        },
        apply: () => {
          operations.forEach(op => op());
          if (fragment.hasChildNodes()) {
            container.appendChild(fragment);
          }
        }
      };
    };
  }

  optimizeEventHandling() {
    // Debounce rapid events
    this.debounce = (func, wait) => {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    };

    // Throttle events
    this.throttle = (func, limit) => {
      let inThrottle;
      return function(...args) {
        if (!inThrottle) {
          func.apply(this, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    };
  }

  // Memory Optimizations
  async optimizeMemory() {
    console.log('[Optimizer] Optimizing memory usage...');

    const memoryOptimization = {
      name: 'Memory Optimization',
      description: 'Optimizing memory usage and preventing leaks',
      optimizations: [
        'Implement automatic cleanup of event listeners',
        'Add memory monitoring and warnings',
        'Optimize object references and closures',
        'Implement garbage collection hints'
      ]
    };

    this.optimizations.set('memory', memoryOptimization);

    this.optimizeMemoryMonitoring();
    this.optimizeEventListenerCleanup();
    this.optimizeObjectPooling();

    console.log('[Optimizer] Memory optimized');
  }

  optimizeMemoryMonitoring() {
    // Monitor memory usage
    this.memoryThreshold = 50 * 1024 * 1024; // 50MB

    this.checkMemoryUsage = () => {
      if (performance.memory) {
        const usage = performance.memory.usedJSHeapSize;
        const percentage = (usage / performance.memory.totalJSHeapSize) * 100;

        if (usage > this.memoryThreshold) {
          console.warn(`[Optimizer] High memory usage: ${(usage / 1024 / 1024).toFixed(2)}MB (${percentage.toFixed(1)}%)`);
          this.triggerMemoryCleanup();
        }

        this.metrics.set('memoryUsage', {
          used: usage,
          total: performance.memory.totalJSHeapSize,
          percentage: percentage
        });
      }
    };
  }

  optimizeEventListenerCleanup() {
    // Track event listeners for cleanup
    this.eventListeners = new WeakMap();

    this.addTrackedListener = (element, event, handler, options = {}) => {
      element.addEventListener(event, handler, options);

      if (!this.eventListeners.has(element)) {
        this.eventListeners.set(element, []);
      }

      this.eventListeners.get(element).push({ event, handler, options });
    };

    this.removeAllListeners = (element) => {
      const listeners = this.eventListeners.get(element);
      if (listeners) {
        listeners.forEach(({ event, handler, options }) => {
          element.removeEventListener(event, handler, options);
        });
        this.eventListeners.delete(element);
      }
    };
  }

  optimizeObjectPooling() {
    // Implement object pooling for frequently created objects
    this.objectPools = new Map();

    this.getPooledObject = (type, factory) => {
      if (!this.objectPools.has(type)) {
        this.objectPools.set(type, []);
      }

      const pool = this.objectPools.get(type);
      if (pool.length > 0) {
        return pool.pop();
      }

      return factory();
    };

    this.returnPooledObject = (type, object) => {
      if (!this.objectPools.has(type)) {
        this.objectPools.set(type, []);
      }

      // Reset object state
      if (typeof object.reset === 'function') {
        object.reset();
      }

      this.objectPools.get(type).push(object);
    };
  }

  // Bundle Size Optimizations
  async optimizeBundleSize() {
    console.log('[Optimizer] Optimizing bundle size...');

    const bundleOptimization = {
      name: 'Bundle Size Optimization',
      description: 'Optimizing extension bundle size and loading performance',
      optimizations: [
        'Implement lazy loading for non-critical modules',
        'Use tree shaking to remove unused code',
        'Minify and compress JavaScript and CSS',
        'Optimize asset loading and caching'
      ]
    };

    this.optimizations.set('bundle', bundleOptimization);

    this.optimizeLazyLoading();
    this.optimizeAssetLoading();

    console.log('[Optimizer] Bundle size optimized');
  }

  optimizeLazyLoading() {
    // Implement lazy loading for modules
    this.loadedModules = new Set();

    this.loadModuleLazy = async (moduleName) => {
      if (this.loadedModules.has(moduleName)) {
        return;
      }

      try {
        // In a real implementation, this would use dynamic imports
        console.log(`[Optimizer] Lazy loading module: ${moduleName}`);
        this.loadedModules.add(moduleName);
      } catch (error) {
        console.error(`[Optimizer] Failed to lazy load module ${moduleName}:`, error);
      }
    };
  }

  optimizeAssetLoading() {
    // Optimize asset loading and caching
    this.assetCache = new Map();

    this.loadAssetOptimized = async (url, type = 'text') => {
      if (this.assetCache.has(url)) {
        return this.assetCache.get(url);
      }

      try {
        const response = await fetch(url);
        let asset;

        if (type === 'json') {
          asset = await response.json();
        } else if (type === 'blob') {
          asset = await response.blob();
        } else {
          asset = await response.text();
        }

        // Cache the asset
        this.assetCache.set(url, asset);
        return asset;

      } catch (error) {
        console.error(`[Optimizer] Failed to load asset ${url}:`, error);
        throw error;
      }
    };
  }

  // Performance Monitoring
  startPerformanceMonitoring() {
    console.log('[Optimizer] Starting performance monitoring...');

    // Monitor memory usage
    if (performance.memory) {
      setInterval(() => {
        this.checkMemoryUsage();
      }, 10000); // Check every 10 seconds
    }

    // Monitor frame rate
    let lastTime = performance.now();
    let frames = 0;

    const monitorFrameRate = () => {
      frames++;
      const currentTime = performance.now();

      if (currentTime >= lastTime + 1000) {
        const fps = Math.round(frames * 1000 / (currentTime - lastTime));
        this.metrics.set('frameRate', fps);

        if (fps < 30) {
          console.warn(`[Optimizer] Low frame rate detected: ${fps} FPS`);
        }

        frames = 0;
        lastTime = currentTime;
      }

      requestAnimationFrame(monitorFrameRate);
    };

    requestAnimationFrame(monitorFrameRate);

    // Monitor long tasks
    const observer = new PerformanceObserver((list) => {
      const longTasks = list.getEntries().filter(entry => entry.duration > 50);
      if (longTasks.length > 0) {
        console.warn(`[Optimizer] ${longTasks.length} long task(s) detected`);
        longTasks.forEach(task => {
          console.warn(`[Optimizer] Long task: ${task.duration.toFixed(2)}ms`);
        });
      }
    });

    try {
      observer.observe({ entryTypes: ['longtask'] });
    } catch (error) {
      console.log('[Optimizer] LongTask API not supported');
    }
  }

  // Cleanup and Maintenance
  triggerMemoryCleanup() {
    console.log('[Optimizer] Triggering memory cleanup...');

    // Clear caches
    this.cache.clear();
    this.cacheAccessOrder.length = 0;

    // Clear object pools
    this.objectPools.clear();

    // Clear asset cache (keep only essential assets)
    const essentialAssets = ['manifest.json', 'content-script.js', 'background.js'];
    for (const [url, asset] of this.assetCache.entries()) {
      const isEssential = essentialAssets.some(essential => url.includes(essential));
      if (!isEssential) {
        this.assetCache.delete(url);
      }
    }

    // Force garbage collection if available
    if (typeof gc !== 'undefined') {
      gc();
    }

    console.log('[Optimizer] Memory cleanup completed');
  }

  // Metrics and Reporting
  getPerformanceMetrics() {
    const metrics = {};

    // Memory metrics
    if (performance.memory) {
      metrics.memory = {
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize,
        percentage: (performance.memory.usedJSHeapSize / performance.memory.totalJSHeapSize) * 100
      };
    }

    // Frame rate
    metrics.frameRate = this.metrics.get('frameRate') || 0;

    // Cache statistics
    metrics.cache = {
      size: this.cache.size,
      maxSize: this.cacheMaxSize
    };

    // Object pool statistics
    metrics.objectPools = {};
    this.objectPools.forEach((pool, type) => {
      metrics.objectPools[type] = pool.length;
    });

    return metrics;
  }

  generatePerformanceReport() {
    const metrics = this.getPerformanceMetrics();
    const optimizations = Array.from(this.optimizations.values());

    const report = {
      timestamp: new Date().toISOString(),
      metrics,
      optimizations,
      recommendations: this.generateRecommendations(metrics)
    };

    console.log('[Optimizer] Performance Report:', report);
    return report;
  }

  generateRecommendations(metrics) {
    const recommendations = [];

    // Memory recommendations
    if (metrics.memory && metrics.memory.percentage > 80) {
      recommendations.push('Consider implementing more aggressive memory cleanup');
    }

    // Frame rate recommendations
    if (metrics.frameRate && metrics.frameRate < 30) {
      recommendations.push('Frame rate is low, consider optimizing animations and DOM updates');
    }

    // Cache recommendations
    if (metrics.cache && metrics.cache.size === metrics.cache.maxSize) {
      recommendations.push('Cache is at maximum capacity, consider increasing cache size or optimizing cache usage');
    }

    return recommendations;
  }

  // API Methods
  optimizeData(data) {
    if (typeof data === 'string') {
      return this.compressData(data);
    }
    return data;
  }

  debouncedUpdate(func, wait = 250) {
    return this.debounce(func, wait);
  }

  throttledUpdate(func, limit = 100) {
    return this.throttle(func, limit);
  }

  getOptimizedStorage() {
    return {
      get: async (key) => {
        // Check cache first
        const cached = this.getCached(key);
        if (cached !== null) {
          return cached;
        }

        // Get from storage
        const data = await this.get(key);
        if (data) {
          this.setCached(key, data);
        }
        return data;
      },

      set: async (key, value) => {
        // Compress if needed
        const optimizedValue = this.optimizeData(value);

        // Set in storage
        await this.set(key, optimizedValue);

        // Update cache
        this.setCached(key, value);
      }
    };
  }
}

// Initialize performance optimizer
const performanceOptimizer = new PerformanceOptimizer();

// Export for use in other modules
window.performanceOptimizer = performanceOptimizer;