// Test script for edit features
// This script can be run in the browser console to test the edit functionality

console.log('🧪 Testing Edit Features...');

// Test 1: Check if SidebarManager class exists and has required methods
function testSidebarManagerMethods() {
  console.log('\n📋 Test 1: SidebarManager Methods');
  
  const requiredMethods = [
    'showEditProjectModal',
    'handleEditProject',
    'showEditPersonaModal', 
    'handleEditPersona',
    'showEditArtifactModal',
    'handleEditArtifact',
    'showCreateArtifactModal',
    'handleCreateArtifact',
    'activatePersona'
  ];
  
  const sidebarManager = new SidebarManager();
  
  requiredMethods.forEach(method => {
    if (typeof sidebarManager[method] === 'function') {
      console.log(`✅ ${method} - OK`);
    } else {
      console.log(`❌ ${method} - MISSING`);
    }
  });
}

// Test 2: Check if global window.sidebarApp is available
function testGlobalAccess() {
  console.log('\n🌐 Test 2: Global Access');
  
  if (typeof window.sidebarApp !== 'undefined') {
    console.log('✅ window.sidebarApp - OK');
    
    // Test if required methods are accessible
    const methods = ['showEditProjectModal', 'showEditPersonaModal', 'showEditArtifactModal'];
    methods.forEach(method => {
      if (typeof window.sidebarApp[method] === 'function') {
        console.log(`✅ window.sidebarApp.${method} - OK`);
      } else {
        console.log(`❌ window.sidebarApp.${method} - MISSING`);
      }
    });
  } else {
    console.log('❌ window.sidebarApp - NOT AVAILABLE');
  }
}

// Test 3: Check if required managers are available
function testManagers() {
  console.log('\n🔧 Test 3: Manager Dependencies');
  
  const sidebarManager = new SidebarManager();
  
  // Check if managers are properly initialized
  const managers = [
    { name: 'projectManager', property: 'projectManager' },
    { name: 'personaManager', property: 'personaManager' },
    { name: 'artifactManager', property: 'artifactManager' }
  ];
  
  managers.forEach(({ name, property }) => {
    if (sidebarManager[property]) {
      console.log(`✅ ${name} - OK`);
      
      // Check if required methods exist
      const requiredMethods = {
        projectManager: ['updateProject', 'getProject'],
        personaManager: ['updatePersona', 'getPersona'],
        artifactManager: ['updateArtifact', 'getArtifact', 'createArtifact']
      };
      
      if (requiredMethods[name]) {
        requiredMethods[name].forEach(method => {
          if (typeof sidebarManager[property][method] === 'function') {
            console.log(`  ✅ ${method} - OK`);
          } else {
            console.log(`  ❌ ${method} - MISSING`);
          }
        });
      }
    } else {
      console.log(`❌ ${name} - NOT AVAILABLE`);
    }
  });
}

// Test 4: Test modal creation
function testModalCreation() {
  console.log('\n🪟 Test 4: Modal Creation');
  
  const sidebarManager = new SidebarManager();
  
  try {
    const testModal = sidebarManager.createModal('Test Modal', '<p>Test content</p>');
    if (testModal && testModal.classList.contains('modal-overlay')) {
      console.log('✅ Modal creation - OK');
      testModal.remove(); // Clean up
    } else {
      console.log('❌ Modal creation - FAILED');
    }
  } catch (error) {
    console.log('❌ Modal creation - ERROR:', error.message);
  }
}

// Test 5: Test form validation (mock test)
function testFormValidation() {
  console.log('\n📝 Test 5: Form Validation');
  
  // Create a mock form element
  const mockForm = document.createElement('form');
  mockForm.innerHTML = `
    <input id="test-name" value="Test Item">
    <textarea id="test-description">Test description</textarea>
    <select id="test-status">
      <option value="active" selected>Active</option>
    </select>
    <input id="test-tags" value="tag1, tag2, tag3">
  `;
  
  // Test tag parsing
  const tags = mockForm.querySelector('#test-tags').value
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0);
  
  if (tags.length === 3 && tags[0] === 'tag1') {
    console.log('✅ Tag parsing - OK');
  } else {
    console.log('❌ Tag parsing - FAILED');
  }
  
  // Test form data extraction
  const formData = {
    name: mockForm.querySelector('#test-name').value,
    description: mockForm.querySelector('#test-description').value,
    status: mockForm.querySelector('#test-status').value,
    tags: tags
  };
  
  if (formData.name === 'Test Item' && formData.tags.length === 3) {
    console.log('✅ Form data extraction - OK');
  } else {
    console.log('❌ Form data extraction - FAILED');
  }
}

// Run all tests
function runAllTests() {
  console.log('🚀 Starting Edit Features Test Suite...');
  
  try {
    testSidebarManagerMethods();
    testGlobalAccess();
    testManagers();
    testModalCreation();
    testFormValidation();
    
    console.log('\n🎉 Test Suite Complete!');
    console.log('Check the results above for any issues.');
  } catch (error) {
    console.error('❌ Test Suite Failed:', error);
  }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runAllTests };
} else {
  // Make available globally for browser console
  window.testEditFeatures = runAllTests;
}

console.log('📋 Test script loaded. Run testEditFeatures() to start tests.');
