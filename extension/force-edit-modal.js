// Script pour forcer l'ouverture des modales d'édition
console.log('🔧 Force Edit Modal Script Loaded');

// Fonction pour forcer l'ouverture d'une modal d'édition
function forceOpenEditModal(itemType = 'project') {
  console.log(`🚀 Forcing ${itemType} edit modal...`);
  
  if (typeof window.coderCompanionInjector === 'undefined') {
    console.log('❌ CoderCompanionInjector not found');
    return;
  }
  
  // Créer un item de test
  const testItem = {
    id: `force-test-${Date.now()}`,
    name: `Test ${itemType.charAt(0).toUpperCase() + itemType.slice(1)}`,
    description: 'This is a test item for forced edit modal',
    status: 'active',
    priority: 'medium',
    role: 'developer',
    content: 'Test content for artifact'
  };
  
  console.log('Test item:', testItem);
  
  try {
    // Appeler directement la méthode d'édition appropriée
    switch (itemType) {
      case 'project':
        if (typeof window.coderCompanionInjector.showEditProjectModal === 'function') {
          window.coderCompanionInjector.showEditProjectModal(testItem.id, testItem);
          console.log('✅ Project edit modal called');
        } else {
          console.log('❌ showEditProjectModal method not found');
        }
        break;
        
      case 'persona':
        if (typeof window.coderCompanionInjector.showEditPersonaModal === 'function') {
          window.coderCompanionInjector.showEditPersonaModal(testItem.id, testItem);
          console.log('✅ Persona edit modal called');
        } else {
          console.log('❌ showEditPersonaModal method not found');
        }
        break;
        
      case 'artifact':
        if (typeof window.coderCompanionInjector.showEditArtifactModal === 'function') {
          window.coderCompanionInjector.showEditArtifactModal(testItem.id, testItem);
          console.log('✅ Artifact edit modal called');
        } else {
          console.log('❌ showEditArtifactModal method not found');
        }
        break;
        
      default:
        console.log('❌ Unknown item type:', itemType);
    }
    
    // Vérifier si la modal est apparue
    setTimeout(() => {
      const modal = document.querySelector('.cc-modal-overlay');
      if (modal) {
        console.log('✅ Modal found in DOM!');
        console.log('Modal element:', modal);
        
        // Vérifier les styles
        const styles = window.getComputedStyle(modal);
        console.log('Modal display:', styles.display);
        console.log('Modal visibility:', styles.visibility);
        console.log('Modal z-index:', styles.zIndex);
        console.log('Modal position:', styles.position);
        
        // Vérifier si elle est visible
        const rect = modal.getBoundingClientRect();
        console.log('Modal position on screen:', rect);
        
        if (rect.width > 0 && rect.height > 0) {
          console.log('✅ Modal is visible on screen!');
        } else {
          console.log('⚠️ Modal exists but may not be visible');
        }
        
      } else {
        console.log('❌ No modal found in DOM');
        
        // Chercher d'autres éléments modaux
        const allModals = document.querySelectorAll('[class*="modal"], [id*="modal"]');
        console.log('Other modal-like elements found:', allModals.length);
        allModals.forEach((el, i) => {
          console.log(`Modal ${i}:`, el.className, el.id);
        });
      }
    }, 100);
    
  } catch (error) {
    console.log('❌ Error forcing edit modal:', error);
  }
}

// Fonction pour tester toutes les modales
function testAllEditModals() {
  console.log('🧪 Testing all edit modals...');
  
  const types = ['project', 'persona', 'artifact'];
  
  types.forEach((type, index) => {
    setTimeout(() => {
      console.log(`\n--- Testing ${type} modal ---`);
      forceOpenEditModal(type);
      
      // Fermer la modal après 3 secondes
      setTimeout(() => {
        const modal = document.querySelector('.cc-modal-overlay');
        if (modal) {
          modal.remove();
          console.log(`🧹 ${type} modal closed`);
        }
      }, 3000);
      
    }, index * 4000); // 4 secondes entre chaque test
  });
}

// Fonction pour vérifier l'état du content script
function checkContentScriptState() {
  console.log('🔍 Checking content script state...');
  
  console.log('CoderCompanionInjector exists:', typeof window.coderCompanionInjector !== 'undefined');
  
  if (typeof window.coderCompanionInjector !== 'undefined') {
    const methods = [
      'handleEditItem',
      'openEditModalDirectly',
      'showEditProjectModal',
      'showEditPersonaModal', 
      'showEditArtifactModal',
      'createModal'
    ];
    
    methods.forEach(method => {
      const exists = typeof window.coderCompanionInjector[method] === 'function';
      console.log(`${method}: ${exists ? '✅' : '❌'}`);
    });
    
    // Vérifier la sidebar
    const sidebar = document.querySelector('#cc-sidebar');
    console.log('Sidebar (#cc-sidebar) exists:', !!sidebar);
    
    if (sidebar) {
      console.log('Sidebar classes:', sidebar.className);
      console.log('Sidebar display:', window.getComputedStyle(sidebar).display);
    }
    
    // Vérifier l'autre sélecteur
    const otherSidebar = document.querySelector('#coder-companion-sidebar');
    console.log('Other sidebar (#coder-companion-sidebar) exists:', !!otherSidebar);
  }
}

// Rendre les fonctions disponibles globalement
window.forceOpenEditModal = forceOpenEditModal;
window.testAllEditModals = testAllEditModals;
window.checkContentScriptState = checkContentScriptState;

console.log('🎯 Force Edit Modal Script Ready!');
console.log('Available functions:');
console.log('- forceOpenEditModal("project") - Force open project edit modal');
console.log('- forceOpenEditModal("persona") - Force open persona edit modal');
console.log('- forceOpenEditModal("artifact") - Force open artifact edit modal');
console.log('- testAllEditModals() - Test all edit modals');
console.log('- checkContentScriptState() - Check content script state');

// Auto-check state
setTimeout(() => {
  checkContentScriptState();
}, 500);
