<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Sidebar</title>
  <link rel="stylesheet" href="sidebar/style.css">
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
    }
    .test-container {
      max-width: 400px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .test-header {
      background: #2563eb;
      color: white;
      padding: 15px;
      text-align: center;
    }
    .test-content {
      height: 600px;
      overflow: hidden;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <div class="test-header">
      <h2>Sidebar Test</h2>
      <p>Testing panel loading functionality</p>
    </div>
    <div class="test-content">
      <div id="sidebar-container" class="sidebar-container">
        <!-- Sidebar Header -->
        <header class="sidebar-header">
          <h2>Coder Companion</h2>
        </header>

        <!-- Tab Navigation -->
        <nav class="sidebar-tabs">
          <button class="tab-button active" data-tab="projects">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M3 7v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2H5a2 2 0 0 0-2-2z"/>
              <path d="M8 5a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v0"/>
            </svg>
            Projects
          </button>
          <button class="tab-button" data-tab="personas">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
              <circle cx="12" cy="7" r="4"/>
            </svg>
            Personas
          </button>
          <button class="tab-button" data-tab="artifacts">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
              <polyline points="14,2 14,8 20,8"/>
            </svg>
            Artifacts
          </button>
          <button class="tab-button" data-tab="export">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
              <polyline points="7,10 12,15 17,10"/>
              <line x1="12" y1="15" x2="12" y2="3"/>
            </svg>
            Export
          </button>
        </nav>

        <!-- Tab Content -->
        <main class="sidebar-content">
          <!-- Projects Panel -->
          <div id="projects-tab" class="tab-content active">
            <!-- Panel content will be loaded dynamically -->
          </div>

          <!-- Personas Panel -->
          <div id="personas-tab" class="tab-content">
            <!-- Panel content will be loaded dynamically -->
          </div>

          <!-- Artifacts Panel -->
          <div id="artifacts-tab" class="tab-content">
            <!-- Panel content will be loaded dynamically -->
          </div>

          <!-- Export Panel -->
          <div id="export-tab" class="tab-content">
            <!-- Panel content will be loaded dynamically -->
          </div>
        </main>
      </div>
    </div>
  </div>

  <script>
    // Mock chrome.runtime for testing
    if (typeof chrome === 'undefined') {
      window.chrome = {
        runtime: {
          getURL: (path) => `./${path}`
        }
      };
    }
  </script>
  <script src="sidebar/script.js"></script>
</body>
</html>
