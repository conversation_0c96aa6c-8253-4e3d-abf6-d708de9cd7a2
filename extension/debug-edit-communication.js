// Debug script for edit communication issues
console.log('🔍 Debug Edit Communication...');

function debugEditCommunication() {
  console.log('🚀 Starting Edit Communication Debug...');
  
  // Check sidebar
  const sidebar = document.querySelector('#coder-companion-sidebar');
  console.log('Sidebar found:', !!sidebar);
  
  if (sidebar) {
    const iframe = sidebar.querySelector('iframe');
    console.log('Iframe found:', !!iframe);
    
    if (iframe) {
      console.log('Iframe src:', iframe.src);
      console.log('Iframe contentWindow:', !!iframe.contentWindow);
    }
  }
  
  // Check content script
  console.log('CoderCompanionInjector:', typeof window.coderCompanionInjector);
  
  // Check sidebar app
  console.log('SidebarApp:', typeof window.sidebarApp);
  
  // Test edit flow
  if (typeof window.coderCompanionInjector !== 'undefined') {
    const mockItem = { id: 'debug-test', name: 'Debug Test', status: 'active' };
    console.log('Testing handleEditItem...');
    try {
      window.coderCompanionInjector.handleEditItem(mockItem, 'project');
      console.log('✅ handleEditItem called successfully');
    } catch (error) {
      console.log('❌ handleEditItem failed:', error);
    }
  }
}

window.debugEditCommunication = debugEditCommunication;
console.log('Run debugEditCommunication() to start debugging.');
