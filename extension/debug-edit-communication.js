// Debug script for edit communication issues
console.log('🔍 Debug Edit Communication...');

function debugEditCommunication() {
  console.log('🚀 Starting Edit Communication Debug...');

  // Check sidebar
  const sidebar = document.querySelector('#coder-companion-sidebar');
  console.log('Sidebar found:', !!sidebar);

  if (sidebar) {
    const iframe = sidebar.querySelector('iframe');
    console.log('Iframe found:', !!iframe);

    if (iframe) {
      console.log('Iframe src:', iframe.src);
      console.log('Iframe contentWindow:', !!iframe.contentWindow);
    }

    // Check sidebar visibility
    const isVisible = sidebar.style.display !== 'none' &&
                     sidebar.style.visibility !== 'hidden' &&
                     !sidebar.classList.contains('hidden');
    console.log('Sidebar visible:', isVisible);
  }

  // Check content script
  console.log('CoderCompanionInjector:', typeof window.coderCompanionInjector);

  // Check sidebar app
  console.log('SidebarApp:', typeof window.sidebarApp);

  // Test edit flow with loop prevention check
  if (typeof window.coderCompanionInjector !== 'undefined') {
    const mockItem = { id: 'debug-test', name: 'Debug Test', status: 'active' };
    console.log('Testing handleEditItem (checking for loops)...');

    // Monitor for multiple calls (potential loop)
    let callCount = 0;
    const originalMethod = window.coderCompanionInjector.openSidebarWithEdit;

    window.coderCompanionInjector.openSidebarWithEdit = function(...args) {
      callCount++;
      console.log(`openSidebarWithEdit called ${callCount} times`);

      if (callCount > 3) {
        console.log('🚨 LOOP DETECTED! Stopping execution.');
        return;
      }

      return originalMethod.apply(this, args);
    };

    try {
      window.coderCompanionInjector.handleEditItem(mockItem, 'project');
      console.log('✅ handleEditItem called successfully');

      // Check for loops after 3 seconds
      setTimeout(() => {
        if (callCount <= 3) {
          console.log('✅ No infinite loop detected');
        } else {
          console.log('❌ Infinite loop detected!');
        }

        // Restore original method
        window.coderCompanionInjector.openSidebarWithEdit = originalMethod;
      }, 3000);

    } catch (error) {
      console.log('❌ handleEditItem failed:', error);
      // Restore original method
      window.coderCompanionInjector.openSidebarWithEdit = originalMethod;
    }
  }
}

function testEditWithoutLoop() {
  console.log('🧪 Testing Edit Without Loop...');

  if (typeof window.coderCompanionInjector !== 'undefined') {
    // Ensure sidebar is open first
    const sidebar = document.querySelector('#coder-companion-sidebar');
    if (!sidebar) {
      console.log('⚠️ Sidebar not found. Please open sidebar first.');
      return;
    }

    const isVisible = sidebar.style.display !== 'none' &&
                     sidebar.style.visibility !== 'hidden' &&
                     !sidebar.classList.contains('hidden');

    if (!isVisible) {
      console.log('⚠️ Sidebar not visible. Please make sure sidebar is open.');
      return;
    }

    // Test with sidebar already open
    const mockItem = { id: 'no-loop-test', name: 'No Loop Test', status: 'active' };
    console.log('Testing with sidebar already open...');

    try {
      // Call communicateWithSidebar directly to bypass sidebar opening logic
      window.coderCompanionInjector.communicateWithSidebar('project', mockItem.id, mockItem);
      console.log('✅ Direct communication test completed');
    } catch (error) {
      console.log('❌ Direct communication failed:', error);
    }
  }
}

window.debugEditCommunication = debugEditCommunication;
window.testEditWithoutLoop = testEditWithoutLoop;
console.log('Run debugEditCommunication() or testEditWithoutLoop() to start debugging.');
