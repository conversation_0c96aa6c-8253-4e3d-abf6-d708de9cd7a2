// Debug script for edit communication issues
console.log('🔍 Debug Edit Communication...');

function debugEditCommunication() {
  console.log('🚀 Starting Edit Communication Debug...');

  // Check sidebar
  const sidebar = document.querySelector('#coder-companion-sidebar');
  console.log('Sidebar found:', !!sidebar);

  if (sidebar) {
    const iframe = sidebar.querySelector('iframe');
    console.log('Iframe found:', !!iframe);

    if (iframe) {
      console.log('Iframe src:', iframe.src);
      console.log('Iframe contentWindow:', !!iframe.contentWindow);
    }

    // Check sidebar visibility
    const isVisible = sidebar.style.display !== 'none' &&
                     sidebar.style.visibility !== 'hidden' &&
                     !sidebar.classList.contains('hidden');
    console.log('Sidebar visible:', isVisible);
  }

  // Check content script
  console.log('CoderCompanionInjector:', typeof window.coderCompanionInjector);

  // Check sidebar app
  console.log('SidebarApp:', typeof window.sidebarApp);

  // Test edit flow with loop prevention check
  if (typeof window.coderCompanionInjector !== 'undefined') {
    const mockItem = { id: 'debug-test', name: 'Debug Test', status: 'active' };
    console.log('Testing handleEditItem (checking for loops)...');

    // Monitor for multiple calls (potential loop)
    let callCount = 0;
    const originalMethod = window.coderCompanionInjector.openSidebarWithEdit;

    window.coderCompanionInjector.openSidebarWithEdit = function(...args) {
      callCount++;
      console.log(`openSidebarWithEdit called ${callCount} times`);

      if (callCount > 3) {
        console.log('🚨 LOOP DETECTED! Stopping execution.');
        return;
      }

      return originalMethod.apply(this, args);
    };

    try {
      window.coderCompanionInjector.handleEditItem(mockItem, 'project');
      console.log('✅ handleEditItem called successfully');

      // Check for loops after 3 seconds
      setTimeout(() => {
        if (callCount <= 3) {
          console.log('✅ No infinite loop detected');
        } else {
          console.log('❌ Infinite loop detected!');
        }

        // Restore original method
        window.coderCompanionInjector.openSidebarWithEdit = originalMethod;
      }, 3000);

    } catch (error) {
      console.log('❌ handleEditItem failed:', error);
      // Restore original method
      window.coderCompanionInjector.openSidebarWithEdit = originalMethod;
    }
  }
}

function testEditWithoutLoop() {
  console.log('🧪 Testing Edit Without Loop...');

  if (typeof window.coderCompanionInjector !== 'undefined') {
    // Ensure sidebar is open first
    const sidebar = document.querySelector('#cc-sidebar');
    if (!sidebar) {
      console.log('⚠️ Sidebar not found. Please open sidebar first.');
      return;
    }

    const isVisible = sidebar.style.display !== 'none' &&
                     sidebar.style.visibility !== 'hidden' &&
                     !sidebar.classList.contains('hidden');

    if (!isVisible) {
      console.log('⚠️ Sidebar not visible. Please make sure sidebar is open.');
      return;
    }

    // Test with sidebar already open
    const mockItem = {
      id: 'no-loop-test',
      name: 'No Loop Test Project',
      description: 'Testing edit functionality',
      status: 'active'
    };
    console.log('Testing with sidebar already open...');

    try {
      // Call openEditModalDirectly to test the new direct approach
      window.coderCompanionInjector.openEditModalDirectly('project', mockItem.id, mockItem);
      console.log('✅ Direct edit modal test completed');
    } catch (error) {
      console.log('❌ Direct edit modal failed:', error);
    }
  }
}

function testEditModalCreation() {
  console.log('🎯 Testing Edit Modal Creation...');

  if (typeof window.coderCompanionInjector !== 'undefined') {
    const mockItems = [
      {
        id: 'test-project-1',
        name: 'Test Project',
        description: 'A test project',
        status: 'active'
      },
      {
        id: 'test-persona-1',
        name: 'Test Persona',
        description: 'A test persona',
        role: 'developer'
      },
      {
        id: 'test-artifact-1',
        name: 'Test Artifact',
        description: 'A test artifact',
        content: 'Some test content'
      }
    ];

    const types = ['project', 'persona', 'artifact'];

    types.forEach((type, index) => {
      const item = mockItems[index];
      console.log(`Testing ${type} edit modal...`);

      try {
        switch (type) {
          case 'project':
            window.coderCompanionInjector.showEditProjectModal(item.id, item);
            break;
          case 'persona':
            window.coderCompanionInjector.showEditPersonaModal(item.id, item);
            break;
          case 'artifact':
            window.coderCompanionInjector.showEditArtifactModal(item.id, item);
            break;
        }
        console.log(`✅ ${type} edit modal created successfully`);

        // Close the modal after a short delay
        setTimeout(() => {
          const modal = document.querySelector('.cc-modal-overlay');
          if (modal) {
            modal.remove();
            console.log(`🧹 ${type} modal cleaned up`);
          }
        }, 2000);

      } catch (error) {
        console.log(`❌ ${type} edit modal failed:`, error);
      }
    });
  } else {
    console.log('❌ CoderCompanionInjector not available');
  }
}

window.debugEditCommunication = debugEditCommunication;
window.testEditWithoutLoop = testEditWithoutLoop;
window.testEditModalCreation = testEditModalCreation;
console.log('Available tests:');
console.log('- debugEditCommunication() - General debug');
console.log('- testEditWithoutLoop() - Test direct communication');
console.log('- testEditModalCreation() - Test edit modal creation');
