// utils/slug-generator.js - Utility for generating kebab-case slugs

class SlugGenerator {
  constructor() {
    // Common words to remove for cleaner slugs
    this.stopWords = new Set([
      'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
      'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
      'to', 'was', 'will', 'with'
    ]);

    // Character mappings for transliteration
    this.charMap = {
      'ä': 'ae', 'ö': 'oe', 'ü': 'ue', 'ß': 'ss',
      'á': 'a', 'é': 'e', 'í': 'i', 'ó': 'o', 'ú': 'u',
      'à': 'a', 'è': 'e', 'ì': 'i', 'ò': 'o', 'ù': 'u',
      'â': 'a', 'ê': 'e', 'î': 'i', 'ô': 'o', 'û': 'u',
      'ã': 'a', 'õ': 'o', 'ñ': 'n',
      'ç': 'c', 'ğ': 'g', 'ı': 'i', 'ş': 's',
      'α': 'a', 'β': 'b', 'γ': 'g', 'δ': 'd', 'ε': 'e',
      'θ': 'th', 'λ': 'l', 'μ': 'm', 'π': 'p', 'ρ': 'r',
      'σ': 's', 'τ': 't', 'φ': 'ph', 'ψ': 'ps', 'ω': 'o'
    };
  }

  /**
   * Generate a kebab-case slug from input text
   * @param {string} text - The text to convert
   * @param {object} options - Configuration options
   * @returns {string} The generated slug
   */
  generate(text, options = {}) {
    const {
      maxLength = 100,
      removeStopWords = false,
      allowNumbers = true,
      customReplacements = {},
      preserveCase = false
    } = options;

    if (!text || typeof text !== 'string') {
      return '';
    }

    let slug = text;

    // Convert to lowercase unless preserveCase is true
    if (!preserveCase) {
      slug = slug.toLowerCase();
    }

    // Apply custom replacements first
    Object.entries(customReplacements).forEach(([pattern, replacement]) => {
      slug = slug.replace(new RegExp(pattern, 'g'), replacement);
    });

    // Transliterate special characters
    slug = this.transliterate(slug);

    // Replace spaces and special characters with hyphens
    slug = slug.replace(/[^\w\s-]/g, ' '); // Replace special chars with spaces
    slug = slug.replace(/[\s_-]+/g, '-'); // Replace spaces/underscores with hyphens

    // Remove stop words if requested
    if (removeStopWords) {
      const words = slug.split('-');
      const filteredWords = words.filter(word => !this.stopWords.has(word));
      slug = filteredWords.join('-');
    }

    // Remove numbers if not allowed
    if (!allowNumbers) {
      slug = slug.replace(/[0-9]/g, '');
    }

    // Clean up multiple hyphens and trim
    slug = slug.replace(/-+/g, '-');
    slug = slug.replace(/^-+|-+$/g, '');

    // Limit length
    if (slug.length > maxLength) {
      slug = slug.substring(0, maxLength);
      slug = slug.replace(/-+$/, ''); // Remove trailing hyphens after truncation
    }

    // Ensure slug is not empty
    if (!slug) {
      slug = this.generateFallbackSlug();
    }

    return slug;
  }

  /**
   * Transliterate special characters to ASCII equivalents
   * @param {string} text - Text to transliterate
   * @returns {string} Transliterated text
   */
  transliterate(text) {
    return text.split('').map(char => {
      return this.charMap[char] || char;
    }).join('');
  }

  /**
   * Generate a fallback slug when input results in empty string
   * @returns {string} Fallback slug
   */
  generateFallbackSlug() {
    const timestamp = Date.now().toString(36);
    return `item-${timestamp}`;
  }

  /**
   * Check if a slug is valid
   * @param {string} slug - The slug to validate
   * @returns {boolean} Whether the slug is valid
   */
  isValidSlug(slug) {
    if (!slug || typeof slug !== 'string') {
      return false;
    }

    // Slug should contain only lowercase letters, numbers, and hyphens
    const slugPattern = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;

    // Should not start or end with hyphens
    const noEdgeHyphens = !slug.startsWith('-') && !slug.endsWith('-');

    // Should not have consecutive hyphens
    const noConsecutiveHyphens = !slug.includes('--');

    // Should not be empty
    const notEmpty = slug.length > 0;

    return slugPattern.test(slug) && noEdgeHyphens && noConsecutiveHyphens && notEmpty;
  }

  /**
   * Sanitize an existing slug to make it valid
   * @param {string} slug - The slug to sanitize
   * @returns {string} Sanitized slug
   */
  sanitizeSlug(slug) {
    if (!slug) {
      return this.generateFallbackSlug();
    }

    // Apply the same logic as generate but with minimal options
    return this.generate(slug, {
      maxLength: 100,
      removeStopWords: false,
      allowNumbers: true,
      preserveCase: false
    });
  }

  /**
   * Generate multiple unique slugs with numeric suffixes
   * @param {string} baseText - Base text for the slug
   * @param {number} count - Number of slugs to generate
   * @param {Set} existingSlugs - Set of existing slugs to avoid conflicts
   * @returns {string[]} Array of unique slugs
   */
  generateUniqueSlugs(baseText, count = 1, existingSlugs = new Set()) {
    const slugs = [];
    const baseSlug = this.generate(baseText);

    for (let i = 0; i < count; i++) {
      let candidateSlug = baseSlug;
      let suffix = 1;

      // Add numeric suffix if needed to avoid conflicts
      while (existingSlugs.has(candidateSlug) || slugs.includes(candidateSlug)) {
        candidateSlug = `${baseSlug}-${suffix}`;
        suffix++;
      }

      slugs.push(candidateSlug);
    }

    return slugs;
  }

  /**
   * Extract keywords from text for better slug generation
   * @param {string} text - Text to extract keywords from
   * @param {number} maxKeywords - Maximum number of keywords to extract
   * @returns {string} Text with extracted keywords
   */
  extractKeywords(text, maxKeywords = 5) {
    if (!text) return '';

    // Simple keyword extraction (in a real implementation, you'd use NLP)
    const words = text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !this.stopWords.has(word));

    // Remove duplicates and limit
    const uniqueWords = [...new Set(words)].slice(0, maxKeywords);

    return uniqueWords.join(' ');
  }

  /**
   * Generate a slug optimized for SEO
   * @param {string} title - Page title or content title
   * @param {string} content - Optional content for keyword extraction
   * @returns {string} SEO-optimized slug
   */
  generateSEO(title, content = '') {
    let text = title;

    // If content is provided, extract keywords
    if (content) {
      const keywords = this.extractKeywords(content, 3);
      text = `${title} ${keywords}`;
    }

    return this.generate(text, {
      maxLength: 60, // Optimal for SEO
      removeStopWords: true,
      allowNumbers: true
    });
  }

  /**
   * Generate a hierarchical slug with parent context
   * @param {string} text - Base text
   * @param {string} parentSlug - Parent slug for context
   * @returns {string} Hierarchical slug
   */
  generateHierarchical(text, parentSlug) {
    const childSlug = this.generate(text);
    return parentSlug ? `${parentSlug}/${childSlug}` : childSlug;
  }

  /**
   * Parse a slug back to human-readable text
   * @param {string} slug - The slug to parse
   * @returns {string} Human-readable text
   */
  parseSlug(slug) {
    if (!slug) return '';

    return slug
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
      .split('/') // Handle hierarchical slugs
      .map(part => part.split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
      )
      .join(' / ');
  }
}

// Export for use in other modules
const slugGenerator = new SlugGenerator();