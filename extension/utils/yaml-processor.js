// utils/yaml-processor.js - YAML front-matter processing for markdown files

class YAMLProcessor {
  constructor() {
    this.yamlDelimiter = '---';
    this.commonFields = [
      'title', 'date', 'lastModified', 'author', 'tags',
      'category', 'description', 'slug', 'project', 'persona',
      'version', 'status', 'priority', 'estimatedTime'
    ];

    // Initialize with basic YAML parsing (in production, use a proper YAML library)
    this.init();
  }

  init() {
    console.log('[YAML] Initializing YAML processor...');
  }

  /**
   * Parse YAML front-matter from markdown content
   * @param {string} content - Markdown content with front-matter
   * @returns {object} Object with frontMatter and content properties
   */
  parse(content) {
    if (!content || typeof content !== 'string') {
      return { frontMatter: {}, content: content || '' };
    }

    const lines = content.split('\n');

    // Check if content starts with YAML delimiter
    if (lines[0].trim() !== this.yamlDelimiter) {
      return { frontMatter: {}, content };
    }

    // Find the end of YAML front-matter
    let endIndex = -1;
    for (let i = 1; i < lines.length; i++) {
      if (lines[i].trim() === this.yamlDelimiter) {
        endIndex = i;
        break;
      }
    }

    if (endIndex === -1) {
      // No closing delimiter found, treat as regular content
      return { frontMatter: {}, content };
    }

    // Extract YAML content
    const yamlContent = lines.slice(1, endIndex).join('\n');
    const markdownContent = lines.slice(endIndex + 1).join('\n');

    try {
      const frontMatter = this.parseYAML(yamlContent);
      return {
        frontMatter: this.validateFrontMatter(frontMatter),
        content: markdownContent
      };
    } catch (error) {
      console.error('[YAML] Failed to parse front-matter:', error);
      return { frontMatter: {}, content };
    }
  }

  /**
   * Inject YAML front-matter into markdown content
   * @param {string} content - Markdown content
   * @param {object} frontMatter - Metadata to inject
   * @param {object} options - Processing options
   * @returns {string} Content with front-matter
   */
  inject(content, frontMatter, options = {}) {
    const {
      overwrite = false,
      preserveFields = []
    } = options;

    if (!content || typeof content !== 'string') {
      content = '';
    }

    // Parse existing front-matter if present
    const existing = this.parse(content);
    let finalFrontMatter = { ...frontMatter };

    if (!overwrite) {
      // Merge with existing front-matter, preserving specified fields
      finalFrontMatter = {
        ...frontMatter,
        ...existing.frontMatter
      };

      // Ensure preserved fields take precedence
      preserveFields.forEach(field => {
        if (existing.frontMatter[field] !== undefined) {
          finalFrontMatter[field] = existing.frontMatter[field];
        }
      });
    }

    // Generate YAML string
    const yamlString = this.generateYAML(finalFrontMatter);

    // Combine with content
    return `${this.yamlDelimiter}\n${yamlString}${this.yamlDelimiter}\n\n${existing.content}`.trim();
  }

  /**
   * Update existing front-matter with new metadata
   * @param {string} content - Content with existing front-matter
   * @param {object} updates - Metadata updates
   * @returns {string} Updated content
   */
  update(content, updates) {
    const { frontMatter, content: markdownContent } = this.parse(content);
    const updatedFrontMatter = { ...frontMatter, ...updates };
    return this.inject(markdownContent, updatedFrontMatter, { overwrite: true });
  }

  /**
   * Remove front-matter from content
   * @param {string} content - Content with front-matter
   * @returns {string} Content without front-matter
   */
  remove(content) {
    const { content: markdownContent } = this.parse(content);
    return markdownContent;
  }

  /**
   * Parse YAML string to object (basic implementation)
   * @param {string} yamlString - YAML content
   * @returns {object} Parsed object
   */
  parseYAML(yamlString) {
    const lines = yamlString.split('\n');
    const result = {};

    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed || trimmed.startsWith('#')) continue;

      const colonIndex = line.indexOf(':');
      if (colonIndex === -1) continue;

      const key = line.substring(0, colonIndex).trim();
      let value = line.substring(colonIndex + 1).trim();

      // Remove quotes if present
      if ((value.startsWith('"') && value.endsWith('"')) ||
          (value.startsWith("'") && value.endsWith("'"))) {
        value = value.slice(1, -1);
      }

      // Parse arrays
      if (value.startsWith('[') && value.endsWith(']')) {
        try {
          result[key] = JSON.parse(value);
        } catch (error) {
          result[key] = value;
        }
      }
      // Parse booleans and numbers
      else if (value === 'true') {
        result[key] = true;
      } else if (value === 'false') {
        result[key] = false;
      } else if (/^\d+$/.test(value)) {
        result[key] = parseInt(value, 10);
      } else if (/^\d+\.\d+$/.test(value)) {
        result[key] = parseFloat(value);
      } else {
        result[key] = value;
      }
    }

    return result;
  }

  /**
   * Generate YAML string from object
   * @param {object} obj - Object to convert
   * @returns {string} YAML string
   */
  generateYAML(obj) {
    const lines = [];

    for (const [key, value] of Object.entries(obj)) {
      let yamlValue = value;

      // Handle different value types
      if (Array.isArray(value)) {
        yamlValue = JSON.stringify(value);
      } else if (typeof value === 'boolean' || typeof value === 'number') {
        yamlValue = value.toString();
      } else if (typeof value === 'string') {
        // Quote strings that contain special characters
        if (value.includes(':') || value.includes('#') || value.includes('\n')) {
          yamlValue = `"${value.replace(/"/g, '\\"')}"`;
        } else {
          yamlValue = value;
        }
      } else if (value instanceof Date) {
        yamlValue = value.toISOString();
      } else if (value === null || value === undefined) {
        yamlValue = '';
      }

      lines.push(`${key}: ${yamlValue}`);
    }

    return lines.join('\n');
  }

  /**
   * Validate front-matter object
   * @param {object} frontMatter - Front-matter object
   * @returns {object} Validated front-matter
   */
  validateFrontMatter(frontMatter) {
    const validated = {};

    for (const [key, value] of Object.entries(frontMatter)) {
      // Ensure keys are valid YAML keys
      if (this.isValidKey(key)) {
        validated[key] = value;
      } else {
        console.warn(`[YAML] Invalid front-matter key: ${key}`);
      }
    }

    return validated;
  }

  /**
   * Check if a key is valid for YAML
   * @param {string} key - Key to validate
   * @returns {boolean} Whether key is valid
   */
  isValidKey(key) {
    // Basic validation - should contain only alphanumeric, underscore, and hyphen
    return /^[a-zA-Z0-9_-]+$/.test(key);
  }

  /**
   * Validate YAML syntax
   * @param {string} yamlString - YAML string to validate
   * @returns {boolean} Whether YAML is valid
   */
  validateYAML(yamlString) {
    try {
      this.parseYAML(yamlString);
      return true;
    } catch (error) {
      console.error('[YAML] Invalid YAML syntax:', error);
      return false;
    }
  }

  /**
   * Generate standard front-matter for artifacts
   * @param {object} artifact - Artifact data
   * @param {object} project - Project data
   * @param {object} persona - Persona data
   * @returns {object} Standard front-matter
   */
  generateStandardFrontMatter(artifact, project = null, persona = null) {
    const now = new Date().toISOString();
    const frontMatter = {
      title: artifact.name || artifact.title || 'Untitled',
      date: now,
      lastModified: now,
      slug: artifact.slug || '',
      version: artifact.version || '1.0.0',
      status: artifact.status || 'draft'
    };

    if (project) {
      frontMatter.project = project.name || project.title || '';
      frontMatter.projectSlug = project.slug || '';
    }

    if (persona) {
      frontMatter.persona = persona.name || persona.title || '';
      frontMatter.personaSlug = persona.slug || '';
    }

    if (artifact.tags && Array.isArray(artifact.tags)) {
      frontMatter.tags = artifact.tags;
    }

    if (artifact.description) {
      frontMatter.description = artifact.description;
    }

    if (artifact.category) {
      frontMatter.category = artifact.category;
    }

    if (artifact.estimatedTime) {
      frontMatter.estimatedTime = artifact.estimatedTime;
    }

    if (artifact.priority) {
      frontMatter.priority = artifact.priority;
    }

    return frontMatter;
  }

  /**
   * Extract specific metadata fields
   * @param {string} content - Content with front-matter
   * @param {string[]} fields - Fields to extract
   * @returns {object} Extracted metadata
   */
  extractFields(content, fields = this.commonFields) {
    const { frontMatter } = this.parse(content);
    const extracted = {};

    fields.forEach(field => {
      if (frontMatter[field] !== undefined) {
        extracted[field] = frontMatter[field];
      }
    });

    return extracted;
  }

  // Enhanced Validation

  /**
   * Validate YAML front-matter against a schema
   * @param {object} frontMatter - Front-matter object
   * @param {object} schema - Validation schema
   * @returns {object} Validation result
   */
  validateAgainstSchema(frontMatter, schema) {
    const errors = [];
    const warnings = [];

    for (const [field, rules] of Object.entries(schema)) {
      const value = frontMatter[field];

      if (rules.required && (value === undefined || value === null || value === '')) {
        errors.push(`${field} is required`);
        continue;
      }

      if (value === undefined || value === null) {
        continue; // Skip validation for optional empty fields
      }

      // Type validation
      if (rules.type) {
        switch (rules.type) {
          case 'string':
            if (typeof value !== 'string') {
              errors.push(`${field} must be a string`);
            }
            break;
          case 'number':
            if (typeof value !== 'number' || isNaN(value)) {
              errors.push(`${field} must be a valid number`);
            }
            break;
          case 'boolean':
            if (typeof value !== 'boolean') {
              errors.push(`${field} must be a boolean`);
            }
            break;
          case 'array':
            if (!Array.isArray(value)) {
              errors.push(`${field} must be an array`);
            }
            break;
          case 'date':
            if (isNaN(Date.parse(value))) {
              errors.push(`${field} must be a valid date`);
            }
            break;
        }
      }

      // Range validation
      if (rules.min !== undefined && value < rules.min) {
        errors.push(`${field} must be at least ${rules.min}`);
      }

      if (rules.max !== undefined && value > rules.max) {
        errors.push(`${field} must be at most ${rules.max}`);
      }

      // Pattern validation
      if (rules.pattern && !rules.pattern.test(value)) {
        errors.push(`${field} does not match required pattern`);
      }

      // Enum validation
      if (rules.enum && !rules.enum.includes(value)) {
        errors.push(`${field} must be one of: ${rules.enum.join(', ')}`);
      }

      // Custom validation
      if (rules.validate && typeof rules.validate === 'function') {
        try {
          const result = rules.validate(value);
          if (result !== true) {
            errors.push(result || `${field} failed custom validation`);
          }
        } catch (error) {
          errors.push(`${field} validation error: ${error.message}`);
        }
      }
    }

    // Check for unexpected fields
    if (!schema.allowUnknown) {
      for (const field of Object.keys(frontMatter)) {
        if (!schema[field]) {
          warnings.push(`Unknown field: ${field}`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Get validation schema for artifacts
   * @returns {object} Validation schema
   */
  getArtifactSchema() {
    return {
      title: { type: 'string', required: true, max: 200 },
      date: { type: 'date', required: false },
      lastModified: { type: 'date', required: false },
      author: { type: 'string', required: false, max: 100 },
      tags: { type: 'array', required: false },
      category: { type: 'string', required: false },
      description: { type: 'string', required: false, max: 500 },
      slug: { type: 'string', required: false },
      project: { type: 'string', required: false },
      persona: { type: 'string', required: false },
      version: { type: 'string', required: false },
      status: {
        type: 'string',
        required: false,
        enum: ['draft', 'in-progress', 'completed', 'archived']
      },
      priority: {
        type: 'string',
        required: false,
        enum: ['low', 'medium', 'high', 'urgent']
      },
      estimatedTime: { type: 'string', required: false },
      allowUnknown: true
    };
  }

  /**
   * Get validation schema for projects
   * @returns {object} Validation schema
   */
  getProjectSchema() {
    return {
      title: { type: 'string', required: true, max: 100 },
      name: { type: 'string', required: true, max: 100 },
      description: { type: 'string', required: false, max: 1000 },
      slug: { type: 'string', required: false },
      status: {
        type: 'string',
        required: false,
        enum: ['active', 'draft', 'archived', 'completed']
      },
      priority: {
        type: 'string',
        required: false,
        enum: ['low', 'medium', 'high', 'urgent']
      },
      tags: { type: 'array', required: false },
      allowUnknown: true
    };
  }

  // Template-Based Generation

  /**
   * Generate front-matter from template
   * @param {string} templateName - Template name
   * @param {object} data - Data to populate template
   * @returns {object} Generated front-matter
   */
  generateFromTemplate(templateName, data = {}) {
    const templates = this.getTemplates();
    const template = templates[templateName];

    if (!template) {
      throw new Error(`Template not found: ${templateName}`);
    }

    const frontMatter = {};

    for (const [field, config] of Object.entries(template.fields)) {
      let value = data[field];

      // Use default value if no data provided
      if (value === undefined && config.default !== undefined) {
        if (typeof config.default === 'function') {
          value = config.default();
        } else {
          value = config.default;
        }
      }

      if (value !== undefined) {
        frontMatter[field] = value;
      }
    }

    return frontMatter;
  }

  /**
   * Get available templates
   * @returns {object} Available templates
   */
  getTemplates() {
    return {
      'article': {
        name: 'Article',
        description: 'Template for blog posts and articles',
        fields: {
          title: { type: 'string', required: true },
          date: { type: 'date', default: () => new Date().toISOString() },
          lastModified: { type: 'date', default: () => new Date().toISOString() },
          author: { type: 'string', default: 'Your Name' },
          tags: { type: 'array', default: [] },
          category: { type: 'string', default: 'article' },
          description: { type: 'string' },
          status: { type: 'string', default: 'draft', enum: ['draft', 'published', 'archived'] },
          featured: { type: 'boolean', default: false }
        }
      },
      'documentation': {
        name: 'Documentation',
        description: 'Template for technical documentation',
        fields: {
          title: { type: 'string', required: true },
          date: { type: 'date', default: () => new Date().toISOString() },
          lastModified: { type: 'date', default: () => new Date().toISOString() },
          author: { type: 'string', default: 'Your Name' },
          tags: { type: 'array', default: [] },
          category: { type: 'string', default: 'documentation' },
          description: { type: 'string' },
          version: { type: 'string', default: '1.0.0' },
          status: { type: 'string', default: 'draft', enum: ['draft', 'in-progress', 'completed', 'archived'] },
          difficulty: { type: 'string', default: 'intermediate', enum: ['beginner', 'intermediate', 'advanced'] }
        }
      },
      'project-artifact': {
        name: 'Project Artifact',
        description: 'Template for project artifacts with metadata',
        fields: {
          title: { type: 'string', required: true },
          date: { type: 'date', default: () => new Date().toISOString() },
          lastModified: { type: 'date', default: () => new Date().toISOString() },
          author: { type: 'string', default: 'Your Name' },
          tags: { type: 'array', default: [] },
          project: { type: 'string' },
          persona: { type: 'string' },
          version: { type: 'string', default: '1.0.0' },
          status: { type: 'string', default: 'draft', enum: ['draft', 'in-progress', 'completed', 'archived'] },
          priority: { type: 'string', default: 'medium', enum: ['low', 'medium', 'high', 'urgent'] },
          estimatedTime: { type: 'string' },
          description: { type: 'string' }
        }
      }
    };
  }

  // Front-Matter Operations

  /**
   * Update specific fields in front-matter
   * @param {string} content - Content with front-matter
   * @param {object} updates - Fields to update
   * @returns {string} Updated content
   */
  updateFields(content, updates) {
    const { frontMatter, content: markdownContent } = this.parse(content);
    const updatedFrontMatter = { ...frontMatter, ...updates };
    return this.inject(markdownContent, updatedFrontMatter, { overwrite: true });
  }

  /**
   * Remove specific fields from front-matter
   * @param {string} content - Content with front-matter
   * @param {string[]} fields - Fields to remove
   * @returns {string} Updated content
   */
  removeFields(content, fields) {
    const { frontMatter, content: markdownContent } = this.parse(content);
    const updatedFrontMatter = { ...frontMatter };

    fields.forEach(field => {
      delete updatedFrontMatter[field];
    });

    return this.inject(markdownContent, updatedFrontMatter, { overwrite: true });
  }

  /**
   * Merge front-matter from multiple sources
   * @param {string[]} contents - Array of contents with front-matter
   * @param {object} options - Merge options
   * @returns {object} Merged front-matter and content
   */
  mergeFrontMatter(contents, options = {}) {
    const { strategy = 'latest' } = options;

    const frontMatters = [];
    const markdownContents = [];

    contents.forEach(content => {
      const { frontMatter, content: markdownContent } = this.parse(content);
      frontMatters.push(frontMatter);
      markdownContents.push(markdownContent);
    });

    const mergedFrontMatter = this.mergeFrontMatters(frontMatters, { strategy });
    const mergedContent = markdownContents.join('\n\n');

    return {
      frontMatter: mergedFrontMatter,
      content: mergedContent,
      fullContent: this.inject(mergedContent, mergedFrontMatter)
    };
  }

  /**
   * Merge multiple front-matter objects
   * @param {object[]} frontMatters - Array of front-matter objects
   * @param {object} options - Merge options
   * @returns {object} Merged front-matter
   */
  mergeFrontMatters(frontMatters, options = {}) {
    const { strategy = 'latest' } = options;
    const merged = {};

    frontMatters.forEach(frontMatter => {
      Object.entries(frontMatter).forEach(([key, value]) => {
        if (!merged[key]) {
          merged[key] = value;
        } else {
          // Handle conflicts based on strategy
          switch (strategy) {
            case 'first':
              // Keep first value
              break;
            case 'merge':
              if (Array.isArray(merged[key]) && Array.isArray(value)) {
                merged[key] = [...new Set([...merged[key], ...value])];
              } else if (key === 'tags' || key === 'categories') {
                // Special handling for tag-like fields
                merged[key] = Array.isArray(merged[key]) ? merged[key] : [merged[key]];
                merged[key] = [...new Set([...merged[key], ...(Array.isArray(value) ? value : [value])])];
              }
              break;
            case 'latest':
            default:
              merged[key] = value; // Last one wins
              break;
          }
        }
      });
    });

    return merged;
  }

  // Utility Methods

  /**
   * Check if content has front-matter
   * @param {string} content - Content to check
   * @returns {boolean} Whether content has front-matter
   */
  hasFrontMatter(content) {
    if (!content || typeof content !== 'string') {
      return false;
    }

    const lines = content.trim().split('\n');
    return lines.length > 0 && lines[0].trim() === this.yamlDelimiter;
  }

  /**
   * Get front-matter field with default value
   * @param {string} content - Content with front-matter
   * @param {string} field - Field name
   * @param {any} defaultValue - Default value
   * @returns {any} Field value or default
   */
  getField(content, field, defaultValue = null) {
    const { frontMatter } = this.parse(content);
    return frontMatter[field] !== undefined ? frontMatter[field] : defaultValue;
  }

  /**
   * Set front-matter field
   * @param {string} content - Content with front-matter
   * @param {string} field - Field name
   * @param {any} value - Field value
   * @returns {string} Updated content
   */
  setField(content, field, value) {
    return this.updateFields(content, { [field]: value });
  }

  /**
   * Get content statistics including front-matter info
   * @param {string} content - Content to analyze
   * @returns {object} Content statistics
   */
  getContentStats(content) {
    const { frontMatter, content: markdownContent } = this.parse(content);

    return {
      hasFrontMatter: Object.keys(frontMatter).length > 0,
      frontMatterFields: Object.keys(frontMatter),
      frontMatterSize: JSON.stringify(frontMatter).length,
      contentSize: markdownContent.length,
      totalSize: content.length,
      wordCount: this.countWords(markdownContent),
      lineCount: markdownContent.split('\n').length
    };
  }

  /**
   * Count words in text
   * @param {string} text - Text to count words in
   * @returns {number} Word count
   */
  countWords(text) {
    if (!text) return 0;
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Merge multiple front-matter objects
   * @param {object[]} frontMatters - Array of front-matter objects
   * @param {object} options - Merge options
   * @returns {object} Merged front-matter
   */
  mergeFrontMatters(frontMatters, options = {}) {
    const { strategy = 'latest' } = options; // 'latest', 'first', 'merge'
    const merged = {};

    frontMatters.forEach(frontMatter => {
      Object.entries(frontMatter).forEach(([key, value]) => {
        if (!merged[key]) {
          merged[key] = value;
        } else {
          switch (strategy) {
            case 'merge':
              if (Array.isArray(merged[key]) && Array.isArray(value)) {
                merged[key] = [...new Set([...merged[key], ...value])];
              } else if (!Array.isArray(merged[key]) && !Array.isArray(value)) {
                merged[key] = value; // Last one wins
              }
              break;
            case 'first':
              // Keep first value
              break;
            case 'latest':
            default:
              merged[key] = value; // Last one wins
              break;
          }
        }
      });
    });

    return merged;
  }

  /**
   * Clean and normalize front-matter
   * @param {object} frontMatter - Front-matter to clean
   * @returns {object} Cleaned front-matter
   */
  cleanFrontMatter(frontMatter) {
    const cleaned = {};

    Object.entries(frontMatter).forEach(([key, value]) => {
      // Remove empty values
      if (value === null || value === undefined || value === '') {
        return;
      }

      // Trim string values
      if (typeof value === 'string') {
        cleaned[key] = value.trim();
      } else {
        cleaned[key] = value;
      }
    });

    return cleaned;
  }
}

// Export for use in other modules
const yamlProcessor = new YAMLProcessor();