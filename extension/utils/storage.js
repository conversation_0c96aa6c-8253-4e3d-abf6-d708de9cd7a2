// utils/storage.js - localStorage wrapper with quota management and optimization

class StorageManager {
  constructor() {
    this.quotaThreshold = 0.8; // Warn at 80% usage
    this.maxRetries = 3;
    this.compressionEnabled = true;
    this.cache = new Map();
    this.listeners = new Map();

    // Data schemas for validation
    this.schemas = {
      projects: this.getProjectSchema(),
      personas: this.getPersonaSchema(),
      artifacts: this.getArtifactSchema()
    };

    // Current data version
    this.currentVersion = '1.0.0';

    this.init();
  }

  init() {
    console.log('[Storage] Initializing storage manager...');

    // Listen for storage changes from other tabs/contexts
    window.addEventListener('storage', this.handleStorageChange.bind(this));

    // Initialize data structure and run migrations
    this.initializeDataStructure();
  }

  // Schema Definitions
  getProjectSchema() {
    return {
      id: 'string',
      name: 'string',
      description: 'string',
      slug: 'string',
      personas: 'array',
      artifacts: 'array',
      status: ['active', 'draft', 'archived', 'completed'],
      priority: ['low', 'medium', 'high', 'urgent'],
      tags: 'array',
      createdAt: 'date',
      updatedAt: 'date',
      lastModified: 'date',
      version: 'number',
      metadata: 'object'
    };
  }

  getPersonaSchema() {
    return {
      id: 'string',
      name: 'string',
      description: 'string',
      slug: 'string',
      role: ['assistant', 'expert', 'reviewer', 'mentor', 'analyst'],
      expertise: 'array',
      personality: ['professional', 'friendly', 'technical', 'creative', 'concise'],
      systemPrompt: 'string',
      temperature: 'number',
      maxTokens: 'number',
      contextWindow: 'number',
      tags: 'array',
      avatar: 'string',
      color: 'string',
      status: ['active', 'inactive'],
      createdAt: 'date',
      updatedAt: 'date',
      lastUsed: 'date',
      usageCount: 'number',
      metadata: 'object'
    };
  }

  getArtifactSchema() {
    return {
      id: 'string',
      name: 'string',
      title: 'string',
      content: 'string',
      slug: 'string',
      projectId: 'string',
      personaId: 'string',
      status: ['draft', 'in-progress', 'completed', 'archived'],
      priority: ['low', 'medium', 'high', 'urgent'],
      tags: 'array',
      wordCount: 'number',
      version: 'number',
      versions: 'array',
      createdAt: 'date',
      updatedAt: 'date',
      lastModified: 'date',
      metadata: 'object'
    };
  }

  // Core storage operations
  async set(key, value, options = {}) {
    const {
      compress = this.compressionEnabled,
      expires = null,
      version = 1
    } = options;

    try {
      // Check quota before storing
      await this.checkQuota();

      // Prepare data
      const data = {
        value,
        timestamp: Date.now(),
        version,
        compressed: false
      };

      if (expires) {
        data.expires = Date.now() + expires;
      }

      // Compress if enabled and data is large
      let serializedData = JSON.stringify(data);
      if (compress && serializedData.length > 1000) {
        serializedData = await this.compressData(serializedData);
        data.compressed = true;
      }

      // Store data
      localStorage.setItem(key, serializedData);

      // Update cache
      this.cache.set(key, data);

      // Notify listeners
      this.notifyListeners(key, 'set', value);

      console.log(`[Storage] Stored ${key} (${serializedData.length} bytes)`);
      return true;
    } catch (error) {
      console.error(`[Storage] Failed to store ${key}:`, error);

      if (error.name === 'QuotaExceededError') {
        await this.handleQuotaExceeded();
        // Retry once after cleanup
        return this.set(key, value, options);
      }

      throw error;
    }
  }

  async get(key, defaultValue = null) {
    try {
      // Check cache first
      if (this.cache.has(key)) {
        const cached = this.cache.get(key);
        if (!this.isExpired(cached)) {
          return cached.value;
        }
        this.cache.delete(key);
      }

      // Get from localStorage
      const serializedData = localStorage.getItem(key);
      if (!serializedData) {
        return defaultValue;
      }

      // Parse data
      const data = JSON.parse(serializedData);

      // Check expiration
      if (this.isExpired(data)) {
        await this.remove(key);
        return defaultValue;
      }

      // Decompress if needed
      let value = data.value;
      if (data.compressed) {
        value = await this.decompressData(data.value);
      }

      // Update cache
      this.cache.set(key, data);

      return value;
    } catch (error) {
      console.error(`[Storage] Failed to get ${key}:`, error);
      return defaultValue;
    }
  }

  async remove(key) {
    try {
      localStorage.removeItem(key);
      this.cache.delete(key);

      // Notify listeners
      this.notifyListeners(key, 'remove');

      console.log(`[Storage] Removed ${key}`);
      return true;
    } catch (error) {
      console.error(`[Storage] Failed to remove ${key}:`, error);
      throw error;
    }
  }

  async clear(pattern = null) {
    try {
      if (pattern) {
        // Remove keys matching pattern
        const keys = Object.keys(localStorage);
        const matchingKeys = keys.filter(key => key.includes(pattern));

        matchingKeys.forEach(key => {
          localStorage.removeItem(key);
          this.cache.delete(key);
          this.notifyListeners(key, 'remove');
        });

        console.log(`[Storage] Cleared ${matchingKeys.length} keys matching "${pattern}"`);
      } else {
        // Clear all
        localStorage.clear();
        this.cache.clear();

        console.log('[Storage] Cleared all storage');
      }

      return true;
    } catch (error) {
      console.error('[Storage] Failed to clear storage:', error);
      throw error;
    }
  }

  // Quota management
  async getQuotaUsage() {
    try {
      const usage = this.calculateStorageUsage();
      const quota = 5 * 1024 * 1024; // 5MB typical limit
      const percentage = usage / quota;

      return {
        usage,
        quota,
        percentage,
        remaining: quota - usage,
        isNearLimit: percentage > this.quotaThreshold
      };
    } catch (error) {
      console.error('[Storage] Failed to get quota usage:', error);
      throw error;
    }
  }

  async checkQuota() {
    const quota = await this.getQuotaUsage();

    if (quota.isNearLimit) {
      console.warn(`[Storage] Storage usage is ${Math.round(quota.percentage * 100)}%`);
    }

    if (quota.percentage > 0.95) {
      throw new DOMException('Storage quota exceeded', 'QuotaExceededError');
    }
  }

  async handleQuotaExceeded() {
    console.warn('[Storage] Handling quota exceeded, running cleanup...');

    try {
      // Remove expired items first
      await this.cleanupExpired();

      // Remove old cache entries
      await this.cleanupCache();

      // Remove old versions if still needed
      const quota = await this.getQuotaUsage();
      if (quota.percentage > 0.9) {
        await this.cleanupOldVersions();
      }

      console.log('[Storage] Cleanup completed');
    } catch (error) {
      console.error('[Storage] Cleanup failed:', error);
    }
  }

  calculateStorageUsage() {
    let total = 0;
    for (let key in localStorage) {
      if (localStorage.hasOwnProperty(key)) {
        total += localStorage[key].length + key.length;
      }
    }
    return total * 2; // UTF-16 characters
  }

  // Data compression (simple implementation)
  async compressData(data) {
    // For now, just return the data - in production you'd use a proper compression library
    // This is a placeholder for future enhancement
    return data;
  }

  async decompressData(data) {
    // Placeholder for decompression
    return data;
  }

  // Cleanup utilities
  async cleanupExpired() {
    const keys = Object.keys(localStorage);
    let removed = 0;

    for (const key of keys) {
      try {
        const data = JSON.parse(localStorage.getItem(key));
        if (this.isExpired(data)) {
          await this.remove(key);
          removed++;
        }
      } catch (error) {
        // Skip invalid JSON
        continue;
      }
    }

    console.log(`[Storage] Removed ${removed} expired items`);
  }

  async cleanupCache() {
    // Remove oldest cache entries if cache is too large
    const maxCacheSize = 50;
    if (this.cache.size > maxCacheSize) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

      const toRemove = entries.slice(0, entries.length - maxCacheSize);
      toRemove.forEach(([key]) => {
        this.cache.delete(key);
      });

      console.log(`[Storage] Cleaned up ${toRemove.length} cache entries`);
    }
  }

  async cleanupOldVersions() {
    // This will be implemented when we have version management
    console.log('[Storage] Version cleanup placeholder');
  }

  // Utility methods
  isExpired(data) {
    return data.expires && Date.now() > data.expires;
  }

  // Event handling
  handleStorageChange(event) {
    if (event.storageArea === localStorage) {
      // Clear cache for changed key
      if (event.key) {
        this.cache.delete(event.key);
      } else {
        // storage.clear() was called
        this.cache.clear();
      }

      // Notify listeners
      if (event.key) {
        this.notifyListeners(event.key, 'change', event.newValue);
      }
    }
  }

  on(key, callback) {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, new Set());
    }
    this.listeners.get(key).add(callback);
  }

  off(key, callback) {
    if (this.listeners.has(key)) {
      this.listeners.get(key).delete(callback);
    }
  }

  notifyListeners(key, action, value = null) {
    if (this.listeners.has(key)) {
      this.listeners.get(key).forEach(callback => {
        try {
          callback({ key, action, value });
        } catch (error) {
          console.error('[Storage] Listener callback failed:', error);
        }
      });
    }
  }

  // Get all keys with optional prefix
  getKeys(prefix = '') {
    const keys = Object.keys(localStorage);
    if (prefix) {
      return keys.filter(key => key.startsWith(prefix));
    }
    return keys;
  }

  // Get storage info
  async getStorageInfo() {
    const quota = await this.getQuotaUsage();
    const keys = this.getKeys();

    return {
      ...quota,
      totalKeys: keys.length,
      cacheSize: this.cache.size,
      listenersCount: this.listeners.size,
      version: this.currentVersion
    };
  }

  // Data Validation
  validateData(dataType, data) {
    if (!this.schemas[dataType]) {
      console.warn(`[Storage] No schema found for data type: ${dataType}`);
      return true;
    }

    const schema = this.schemas[dataType];
    const errors = [];

    for (const [field, expectedType] of Object.entries(schema)) {
      const value = data[field];

      if (value === undefined || value === null) {
        if (field === 'id' || field === 'createdAt') {
          errors.push(`${field} is required`);
        }
        continue;
      }

      if (Array.isArray(expectedType)) {
        // Enum validation
        if (!expectedType.includes(value)) {
          errors.push(`${field} must be one of: ${expectedType.join(', ')}`);
        }
      } else {
        // Type validation
        switch (expectedType) {
          case 'string':
            if (typeof value !== 'string') {
              errors.push(`${field} must be a string`);
            }
            break;
          case 'number':
            if (typeof value !== 'number' || isNaN(value)) {
              errors.push(`${field} must be a valid number`);
            }
            break;
          case 'array':
            if (!Array.isArray(value)) {
              errors.push(`${field} must be an array`);
            }
            break;
          case 'object':
            if (typeof value !== 'object' || Array.isArray(value)) {
              errors.push(`${field} must be an object`);
            }
            break;
          case 'date':
            if (isNaN(Date.parse(value))) {
              errors.push(`${field} must be a valid date`);
            }
            break;
        }
      }
    }

    if (errors.length > 0) {
      console.error(`[Storage] Validation errors for ${dataType}:`, errors);
      return false;
    }

    return true;
  }

  // Data Migration
  async initializeDataStructure() {
    try {
      // Check current version
      const version = await this.get('dataVersion', '0.0.0');

      if (this.needsMigration(version)) {
        console.log(`[Storage] Migrating data from ${version} to ${this.currentVersion}`);
        await this.runMigrations(version);
        await this.set('dataVersion', this.currentVersion);
      }

      // Ensure required data structures exist
      await this.initializeDataCollections();

    } catch (error) {
      console.error('[Storage] Failed to initialize data structure:', error);
    }
  }

  needsMigration(currentVersion) {
    return currentVersion !== this.currentVersion;
  }

  async runMigrations(fromVersion) {
    const migrations = this.getMigrations(fromVersion);

    for (const migration of migrations) {
      try {
        console.log(`[Storage] Running migration: ${migration.name}`);
        await migration.up();
      } catch (error) {
        console.error(`[Storage] Migration failed: ${migration.name}`, error);
        // Continue with other migrations
      }
    }
  }

  getMigrations(fromVersion) {
    // Define migrations based on version
    const migrations = [];

    if (fromVersion < '1.0.0') {
      migrations.push({
        name: 'v1.0.0-initial-structure',
        up: async () => {
          // Ensure basic data structures
          const projects = await this.get('projects', []);
          const personas = await this.get('personas', []);
          const artifacts = await this.get('artifacts', []);

          // Validate and clean data
          const cleanProjects = projects.filter(p => this.validateData('projects', p));
          const cleanPersonas = personas.filter(p => this.validateData('personas', p));
          const cleanArtifacts = artifacts.filter(a => this.validateData('artifacts', a));

          // Save cleaned data
          await this.set('projects', cleanProjects);
          await this.set('personas', cleanPersonas);
          await this.set('artifacts', cleanArtifacts);
        }
      });
    }

    return migrations;
  }

  async initializeDataCollections() {
    // Initialize empty arrays if they don't exist
    const collections = ['projects', 'personas', 'artifacts'];

    for (const collection of collections) {
      const data = await this.get(collection, null);
      if (data === null) {
        await this.set(collection, []);
      }
    }
  }

  // Backup and Restore
  async createBackup(options = {}) {
    console.log('[Storage] Creating data backup...');

    try {
      const {
        includeArtifacts = true,
        compress = true
      } = options;

      // Collect all data
      const backupData = {
        version: this.currentVersion,
        timestamp: new Date().toISOString(),
        data: {}
      };

      const keys = this.getKeys();
      for (const key of keys) {
        if (!key.startsWith('backup_') && (includeArtifacts || !key.includes('artifacts'))) {
          backupData.data[key] = await this.get(key);
        }
      }

      // Generate backup ID
      const backupId = `backup_${Date.now()}`;
      const backupKey = `backup_${backupId}`;

      // Save backup
      await this.set(backupKey, backupData, { compress });

      console.log(`[Storage] Backup created: ${backupId}`);
      return backupId;

    } catch (error) {
      console.error('[Storage] Failed to create backup:', error);
      throw error;
    }
  }

  async restoreBackup(backupId, options = {}) {
    console.log(`[Storage] Restoring backup: ${backupId}`);

    try {
      const {
        overwrite = false,
        includeArtifacts = true
      } = options;

      const backupKey = `backup_${backupId}`;
      const backup = await this.get(backupKey);

      if (!backup) {
        throw new Error(`Backup not found: ${backupId}`);
      }

      // Validate backup version compatibility
      if (backup.version > this.currentVersion) {
        throw new Error(`Cannot restore from newer version ${backup.version}`);
      }

      // Restore data
      for (const [key, value] of Object.entries(backup.data)) {
        if (includeArtifacts || !key.includes('artifacts')) {
          if (overwrite || (await this.get(key, null)) === null) {
            await this.set(key, value);
          }
        }
      }

      console.log(`[Storage] Backup restored: ${backupId}`);
      return true;

    } catch (error) {
      console.error('[Storage] Failed to restore backup:', error);
      throw error;
    }
  }

  async listBackups() {
    try {
      const keys = this.getKeys();
      const backupKeys = keys.filter(key => key.startsWith('backup_'));

      const backups = [];
      for (const key of backupKeys) {
        const backup = await this.get(key);
        if (backup) {
          backups.push({
            id: key.replace('backup_', ''),
            timestamp: backup.timestamp,
            version: backup.version,
            size: JSON.stringify(backup).length
          });
        }
      }

      return backups.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    } catch (error) {
      console.error('[Storage] Failed to list backups:', error);
      return [];
    }
  }

  async deleteBackup(backupId) {
    try {
      const backupKey = `backup_${backupId}`;
      await this.remove(backupKey);
      console.log(`[Storage] Backup deleted: ${backupId}`);
      return true;

    } catch (error) {
      console.error('[Storage] Failed to delete backup:', error);
      return false;
    }
  }

  // Bulk Operations
  async bulkSet(data) {
    console.log('[Storage] Performing bulk set operation...');

    try {
      const results = {};

      for (const [key, value] of Object.entries(data)) {
        results[key] = await this.set(key, value);
      }

      console.log(`[Storage] Bulk set completed for ${Object.keys(data).length} keys`);
      return results;

    } catch (error) {
      console.error('[Storage] Bulk set failed:', error);
      throw error;
    }
  }

  async bulkGet(keys) {
    console.log('[Storage] Performing bulk get operation...');

    try {
      const results = {};

      for (const key of keys) {
        results[key] = await this.get(key);
      }

      console.log(`[Storage] Bulk get completed for ${keys.length} keys`);
      return results;

    } catch (error) {
      console.error('[Storage] Bulk get failed:', error);
      throw error;
    }
  }

  // Data Integrity
  async validateDataIntegrity() {
    console.log('[Storage] Validating data integrity...');

    try {
      const issues = [];

      // Check projects
      const projects = await this.get('projects', []);
      projects.forEach((project, index) => {
        if (!this.validateData('projects', project)) {
          issues.push(`Invalid project at index ${index}`);
        }
      });

      // Check personas
      const personas = await this.get('personas', []);
      personas.forEach((persona, index) => {
        if (!this.validateData('personas', persona)) {
          issues.push(`Invalid persona at index ${index}`);
        }
      });

      // Check artifacts
      const artifacts = await this.get('artifacts', []);
      artifacts.forEach((artifact, index) => {
        if (!this.validateData('artifacts', artifact)) {
          issues.push(`Invalid artifact at index ${index}`);
        }
      });

      if (issues.length > 0) {
        console.warn('[Storage] Data integrity issues found:', issues);
        return { valid: false, issues };
      }

      console.log('[Storage] Data integrity validation passed');
      return { valid: true, issues: [] };

    } catch (error) {
      console.error('[Storage] Data integrity validation failed:', error);
      return { valid: false, issues: [error.message] };
    }
  }

  // Cleanup and Maintenance
  async cleanupOldBackups(keepLast = 5) {
    console.log(`[Storage] Cleaning up old backups, keeping last ${keepLast}...`);

    try {
      const backups = await this.listBackups();

      if (backups.length > keepLast) {
        const toDelete = backups.slice(keepLast);
        let deleted = 0;

        for (const backup of toDelete) {
          if (await this.deleteBackup(backup.id)) {
            deleted++;
          }
        }

        console.log(`[Storage] Cleaned up ${deleted} old backups`);
        return deleted;
      }

      return 0;

    } catch (error) {
      console.error('[Storage] Failed to cleanup old backups:', error);
      return 0;
    }
  }

  async performMaintenance() {
    console.log('[Storage] Performing maintenance...');

    try {
      // Validate data integrity
      const integrity = await this.validateDataIntegrity();

      // Cleanup old backups
      const cleanedBackups = await this.cleanupOldBackups();

      // Cleanup cache
      await this.cleanupCache();

      // Get storage info
      const info = await this.getStorageInfo();

      return {
        integrity,
        cleanedBackups,
        storageInfo: info
      };

    } catch (error) {
      console.error('[Storage] Maintenance failed:', error);
      throw error;
    }
  }
}

// Export for use in other modules
const storageManager = new StorageManager();