// content-script.js - Main injection script for Google AI Studio integration
class CoderCompanionInjector {
  constructor() {
    this.sidebarContainer = null;
    this.toggleButton = null;
    this.isInjected = false;
    this.isSidebarOpen = false;
  }

  async init() {
    console.log('[Coder Companion] Initializing extension...');

    try {
      await this.waitForPageLoad();
      this.injectToggleButton();
      this.injectSidebar();
      this.setupEventListeners();
      this.loadExtensionState();

      // Mark as initialized and set up message listener
      this.isInitialized = true;
      this.setupMessageListener();

      console.log('[Coder Companion] Extension initialized successfully');
    } catch (error) {
      console.error('[Coder Companion] Initialization failed:', error);
    }
  }

  waitForPageLoad() {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve();
      } else {
        window.addEventListener('load', resolve);
      }
    });
  }

  injectToggleButton() {
    console.log('[Coder Companion] Injecting toggle button...');

    this.toggleButton = document.createElement('button');
    this.toggleButton.id = 'cc-toggle-btn';
    // Remove Tailwind classes - CSS is handled by extension.css
    this.toggleButton.innerHTML = `
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M3 12h18M3 6h18M3 18h18"/>
      </svg>
    `;
    this.toggleButton.title = 'Toggle Coder Companion Sidebar';

    document.body.appendChild(this.toggleButton);
  }

  injectSidebar() {
    console.log('[Coder Companion] Injecting sidebar...');

    this.sidebarContainer = document.createElement('div');
    this.sidebarContainer.id = 'cc-sidebar';
    // Remove Tailwind classes - CSS is handled by extension.css

    // Load sidebar HTML from extension resources
    fetch(chrome.runtime.getURL('sidebar/index.html'))
      .then(response => response.text())
      .then(html => {
        console.log('[Coder Companion] Sidebar HTML loaded, length:', html.length);

        // Remove the script tag from HTML to prevent immediate execution
        const cleanHtml = html.replace(/<script[^>]*src="script\.js"[^>]*><\/script>/g, '');

        this.sidebarContainer.innerHTML = cleanHtml;
        document.body.appendChild(this.sidebarContainer);
        console.log('[Coder Companion] Sidebar added to DOM');

        // Now manually load and execute the sidebar script
        this.loadSidebarScript();

        this.initializeSidebarComponents();
        this.isInjected = true;
        console.log('[Coder Companion] Sidebar initialization complete');
      })
      .catch(error => {
        console.error('[Coder Companion] Failed to load sidebar:', error);
      });
  }


  setupEventListeners() {
    console.log('[Coder Companion] Setting up event listeners...');

    // Toggle button click
    this.toggleButton.addEventListener('click', () => {
      this.toggleSidebar();
    });

    // Keyboard shortcut (Ctrl+Shift+C)
    document.addEventListener('keydown', (event) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'C') {
        event.preventDefault();
        this.toggleSidebar();
      }
    });

    // Close sidebar when clicking outside
    document.addEventListener('click', (event) => {
      if (this.isSidebarOpen &&
          !this.sidebarContainer.contains(event.target) &&
          !this.toggleButton.contains(event.target)) {
        this.closeSidebar();
      }
    });
  }

  toggleSidebar() {
    if (this.isSidebarOpen) {
      this.closeSidebar();
    } else {
      this.openSidebar();
    }
  }

  openSidebar() {
    if (this.sidebarContainer && !this.isSidebarOpen) {
      console.log('[Coder Companion] Opening sidebar - current classes:', this.sidebarContainer.className);

      // Use CSS class instead of direct style manipulation
      this.sidebarContainer.classList.add('open');
      this.isSidebarOpen = true;

      console.log('[Coder Companion] Sidebar opened - new classes:', this.sidebarContainer.className);

      // Debug: Check sidebar position and visibility
      const rect = this.sidebarContainer.getBoundingClientRect();
      const computed = window.getComputedStyle(this.sidebarContainer);
      console.log('[Coder Companion] Sidebar position:', {
        top: rect.top,
        left: rect.left,
        width: rect.width,
        height: rect.height,
        display: computed.display,
        visibility: computed.visibility,
        opacity: computed.opacity,
        zIndex: computed.zIndex,
        transform: computed.transform
      });

      // Update toggle button appearance - remove Tailwind classes and use a simple active state
      this.toggleButton.style.background = 'linear-gradient(135deg, #2563eb, #1e40af)';
      this.toggleButton.innerHTML = `
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M18 6L6 18M6 6l12 12"/>
        </svg>
      `;

      // Notify sidebar components
      this.dispatchSidebarEvent('sidebarOpened');
    }
  }

  closeSidebar() {
    if (this.sidebarContainer && this.isSidebarOpen) {
      // Use CSS class instead of direct style manipulation
      this.sidebarContainer.classList.remove('open');
      this.isSidebarOpen = false;

      // Update toggle button appearance - reset to default gradient
      this.toggleButton.style.background = 'linear-gradient(135deg, #3b82f6, #1d4ed8)';
      this.toggleButton.innerHTML = `
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M3 12h18M3 6h18M3 18h18"/>
        </svg>
      `;

      // Notify sidebar components
      this.dispatchSidebarEvent('sidebarClosed');
    }
  }

  dispatchSidebarEvent(eventType) {
    const event = new CustomEvent(eventType, {
      detail: { sidebar: this.sidebarContainer }
    });
    document.dispatchEvent(event);
  }

  loadExtensionState() {
    console.log('[Coder Companion] Loading extension state...');

    // Load saved preferences and state from storage
    chrome.storage.local.get(['sidebarOpen', 'lastActiveTab'], (result) => {
      if (result.sidebarOpen) {
        // Small delay to ensure DOM is ready, then open sidebar
        setTimeout(() => {
          this.openSidebar();
        }, 100);
      }
    });
  }

  loadSidebarScript() {
    console.log('[Coder Companion] Loading sidebar script...');

    // Instead of injecting inline script (which violates CSP),
    // we'll create the sidebar functionality directly in this content script
    this.initializeSidebarFunctionality();
  }

  initializeSidebarFunctionality() {
    console.log('[Coder Companion] Initializing sidebar functionality...');

    // Wait for DOM to be ready
    setTimeout(() => {
      this.createSidebarContent();
      this.setupSidebarEventListeners();
    }, 100);
  }

  createSidebarContent() {
    console.log('[Coder Companion] Creating sidebar content...');

    const tabs = ['projects', 'personas', 'artifacts', 'export'];

    tabs.forEach(tabName => {
      const tabElement = document.getElementById(`${tabName}-tab`);
      console.log(`[Coder Companion] Tab ${tabName}:`, tabElement);

      if (tabElement) {
        tabElement.innerHTML = `
          <div class="panel-header">
            <div class="panel-actions">
              <button id="new-${tabName}-btn" class="cc-button primary">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <line x1="12" y1="5" x2="12" y2="19"/>
                  <line x1="5" y1="12" x2="19" y2="12"/>
                </svg>
                New ${tabName.charAt(0).toUpperCase() + tabName.slice(1).slice(0, -1)}
              </button>
              <button id="refresh-${tabName}-btn" class="cc-button secondary">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <polyline points="23,4 23,10 17,10"/>
                  <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
                </svg>
              </button>
            </div>
          </div>
          <div class="panel-content">
            <div id="${tabName}-list" class="cc-list">
              <div class="cc-loading">
                <div class="spinner"></div>
                Loading ${tabName}...
              </div>
            </div>
          </div>
          <div class="panel-footer">
            <div class="stats-bar">
              <div class="stat-item">
                <span class="stat-label">Total:</span>
                <span class="stat-value" id="${tabName}-total">0</span>
              </div>
            </div>
          </div>
        `;

        // Load data for this tab
        setTimeout(() => this.loadTabData(tabName), 500);

        console.log(`[Coder Companion] Created content for ${tabName}`);
      } else {
        console.error(`[Coder Companion] Tab element not found: ${tabName}-tab`);
      }
    });
  }

  setupSidebarEventListeners() {
    console.log('[Coder Companion] Setting up sidebar event listeners...');

    // Tab navigation
    document.querySelectorAll('.tab-button').forEach(button => {
      button.addEventListener('click', () => {
        const tabId = button.dataset.tab;
        console.log(`[Coder Companion] Tab clicked: ${tabId}`);
        this.switchTab(tabId);
      });
    });

    // Action buttons
    const tabs = ['projects', 'personas', 'artifacts', 'export'];
    tabs.forEach(tabName => {
      const newBtn = document.getElementById(`new-${tabName}-btn`);
      const refreshBtn = document.getElementById(`refresh-${tabName}-btn`);

      if (newBtn) {
        newBtn.addEventListener('click', () => {
          console.log(`[Coder Companion] New ${tabName} clicked`);
          this.handleNewItem(tabName);
        });
      }

      if (refreshBtn) {
        refreshBtn.addEventListener('click', () => {
          console.log(`[Coder Companion] Refresh ${tabName} clicked`);
          this.loadTabData(tabName);
        });
      }
    });
  }

  switchTab(tabId) {
    console.log(`[Coder Companion] Switching to tab: ${tabId}`);

    // Update tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
      button.classList.remove('active');
    });
    const activeButton = document.querySelector(`[data-tab="${tabId}"]`);
    if (activeButton) {
      activeButton.classList.add('active');
    }

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    const activeContent = document.getElementById(`${tabId}-tab`);
    if (activeContent) {
      activeContent.classList.add('active');
    }
  }

  async loadTabData(tabName) {
    console.log(`[Coder Companion] Loading data for ${tabName}`);

    const listElement = document.getElementById(`${tabName}-list`);
    if (!listElement) return;

    // Show loading state
    listElement.innerHTML = `
      <div class="cc-loading">
        <div class="spinner"></div>
        Loading ${tabName}...
      </div>
    `;

    try {
      // Load data (real or sample)
      const data = await this.getSampleData(tabName);
      this.renderList(tabName, data);
    } catch (error) {
      console.error(`[Coder Companion] Failed to load ${tabName} data:`, error);
      listElement.innerHTML = `
        <div class="empty-state">
          <h3>Failed to load data</h3>
          <p>Please try again later</p>
        </div>
      `;
    }
  }

  getSampleData(tabName) {
    switch (tabName) {
      case 'projects':
        return [
          { id: '1', name: 'AI Assistant Development', status: 'active', lastModified: '2 hours ago' },
          { id: '2', name: 'Code Review Tool', status: 'draft', lastModified: '1 day ago' }
        ];
      case 'personas':
        return [
          { id: '1', name: 'Senior Frontend Developer', status: 'active', lastModified: '30 minutes ago' },
          { id: '2', name: 'Backend Architect', status: 'active', lastModified: '2 hours ago' }
        ];
      case 'artifacts':
        return [
          { id: '1', name: 'API Documentation', status: 'completed', lastModified: '1 hour ago' },
          { id: '2', name: 'Database Schema', status: 'draft', lastModified: '3 hours ago' }
        ];
      case 'export':
        return [
          { id: '1', name: 'Current Project Export', status: 'ready', lastModified: 'Available now' },
          { id: '2', name: 'All Projects Archive', status: 'ready', lastModified: 'Available now' }
        ];
      default:
        return [];
    }
  }

  renderList(tabName, items) {
    const listElement = document.getElementById(`${tabName}-list`);
    if (!listElement) return;

    if (items.length === 0) {
      listElement.innerHTML = `
        <div class="empty-state">
          <h3>No ${tabName} found</h3>
          <p>Create your first ${tabName.slice(0, -1)} to get started</p>
        </div>
      `;
      return;
    }

    const itemsHtml = items.map(item => `
      <div class="cc-list-item" data-id="${item.id}">
        <div class="item-title">${item.name}</div>
        <div class="item-meta">
          <span class="status-indicator ${item.status}">${item.status}</span>
          <span>Last modified: ${item.lastModified}</span>
        </div>
      </div>
    `).join('');

    listElement.innerHTML = itemsHtml;

    // Add click handlers
    listElement.querySelectorAll('.cc-list-item').forEach(item => {
      item.addEventListener('click', () => {
        console.log(`[Coder Companion] Clicked ${tabName} item:`, item.dataset.id);
        this.handleItemClick(tabName, item.dataset.id);
      });
    });

    // Update stats
    const totalElement = document.getElementById(`${tabName}-total`);
    if (totalElement) {
      totalElement.textContent = items.length;
    }
  }

  handleNewItem(tabName) {
    const itemType = tabName.slice(0, -1); // Remove 's' from end
    console.log(`[Coder Companion] Creating new ${itemType}`);

    switch (tabName) {
      case 'projects':
        this.showCreateProjectModal();
        break;
      case 'personas':
        this.showCreatePersonaModal();
        break;
      case 'artifacts':
        this.showCreateArtifactModal();
        break;
      case 'export':
        this.showExportModal();
        break;
      default:
        alert(`Create New ${itemType.charAt(0).toUpperCase() + itemType.slice(1)}\n\nModal not implemented yet.`);
    }
  }

  async handleItemClick(tabName, itemId) {
    const itemType = tabName.slice(0, -1);
    console.log(`[Coder Companion] Opening ${itemType}: ${itemId}`);

    try {
      // Get item data (await the async function)
      const items = await this.getSampleData(tabName);
      console.log(`[Coder Companion] Items for ${tabName}:`, items);

      // Ensure items is an array
      if (!Array.isArray(items)) {
        console.error(`[Coder Companion] Items is not an array:`, items);
        alert(`Failed to load ${itemType} data`);
        return;
      }

      const item = items.find(i => i.id === itemId);

      if (item) {
        this.showItemDetailsModal(itemType, item);
      } else {
        console.warn(`[Coder Companion] Item not found: ${itemId} in`, items);
        alert(`${itemType.charAt(0).toUpperCase() + itemType.slice(1)} not found: ${itemId}`);
      }
    } catch (error) {
      console.error(`[Coder Companion] Failed to handle item click:`, error);
      alert(`Failed to open ${itemType} details`);
    }
  }

  // Modal System
  createModal(title, content, className = '') {
    // Remove existing modal if any
    const existingModal = document.getElementById('cc-modal');
    if (existingModal) {
      existingModal.remove();
    }

    const modalOverlay = document.createElement('div');
    modalOverlay.id = 'cc-modal';
    modalOverlay.className = `cc-modal-overlay ${className}`;
    modalOverlay.innerHTML = `
      <div class="cc-modal">
        <div class="cc-modal-header">
          <h3>${title}</h3>
          <button class="cc-modal-close">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="cc-modal-content">
          ${content}
        </div>
      </div>
    `;

    // Add styles if not already added
    this.addModalStyles();

    document.body.appendChild(modalOverlay);

    // Setup event listeners
    this.setupModalEventListeners(modalOverlay);

    return modalOverlay;
  }

  setupModalEventListeners(modalOverlay) {
    // Close button
    const closeButton = modalOverlay.querySelector('.cc-modal-close');
    if (closeButton) {
      closeButton.addEventListener('click', () => {
        console.log('[Coder Companion] Modal close button clicked');
        modalOverlay.remove();
      });
    }

    // Cancel buttons
    const cancelButtons = modalOverlay.querySelectorAll('.cc-btn-secondary');
    cancelButtons.forEach(button => {
      if (button.textContent.trim() === 'Cancel') {
        button.addEventListener('click', () => {
          console.log('[Coder Companion] Modal cancel button clicked');
          modalOverlay.remove();
        });
      }
    });

    // Close on overlay click
    modalOverlay.addEventListener('click', (e) => {
      if (e.target === modalOverlay) {
        console.log('[Coder Companion] Modal overlay clicked');
        modalOverlay.remove();
      }
    });

    // Export button
    const exportButton = modalOverlay.querySelector('#export-data-btn');
    if (exportButton) {
      exportButton.addEventListener('click', () => {
        console.log('[Coder Companion] Export data button clicked');
        this.handleExport();
      });
    }

    // Edit button
    const editButton = modalOverlay.querySelector('#edit-item-btn');
    if (editButton) {
      editButton.addEventListener('click', () => {
        console.log('[Coder Companion] Edit item button clicked');
        alert('Edit functionality coming soon!');
      });
    }

    // Close buttons (Close, not Cancel)
    const closeButtons = modalOverlay.querySelectorAll('.cc-btn-secondary');
    closeButtons.forEach(button => {
      if (button.textContent.trim() === 'Close') {
        button.addEventListener('click', () => {
          console.log('[Coder Companion] Modal close button clicked');
          modalOverlay.remove();
        });
      }
    });

    // Close on Escape key
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        console.log('[Coder Companion] Escape key pressed');
        modalOverlay.remove();
        document.removeEventListener('keydown', handleEscape);
      }
    };
    document.addEventListener('keydown', handleEscape);
  }

  addModalStyles() {
    if (document.getElementById('cc-modal-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'cc-modal-styles';
    styles.textContent = `
      .cc-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        padding: 20px;
      }

      .cc-modal {
        background: white;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        max-width: 500px;
        width: 100%;
        max-height: 90vh;
        overflow: hidden;
        display: flex;
        flex-direction: column;
      }

      .cc-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        border-bottom: 1px solid #e5e7eb;
        background: #f9fafb;
      }

      .cc-modal-header h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #111827;
      }

      .cc-modal-close {
        background: none;
        border: none;
        padding: 4px;
        cursor: pointer;
        color: #6b7280;
        border-radius: 4px;
        transition: all 0.2s ease;
      }

      .cc-modal-close:hover {
        background: #e5e7eb;
        color: #374151;
      }

      .cc-modal-content {
        padding: 20px;
        overflow-y: auto;
        flex: 1;
      }

      .cc-form {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .cc-form-group {
        display: flex;
        flex-direction: column;
        gap: 6px;
      }

      .cc-form-group label {
        font-size: 14px;
        font-weight: 500;
        color: #374151;
      }

      .cc-form-group input,
      .cc-form-group textarea,
      .cc-form-group select {
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        font-size: 14px;
        transition: border-color 0.2s ease;
      }

      .cc-form-group input:focus,
      .cc-form-group textarea:focus,
      .cc-form-group select:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      .cc-form-actions {
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        margin-top: 8px;
        padding-top: 16px;
        border-top: 1px solid #e5e7eb;
      }

      .cc-btn {
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
      }

      .cc-btn-primary {
        background: #3b82f6;
        color: white;
      }

      .cc-btn-primary:hover {
        background: #2563eb;
      }

      .cc-btn-secondary {
        background: #f3f4f6;
        color: #374151;
        border: 1px solid #d1d5db;
      }

      .cc-btn-secondary:hover {
        background: #e5e7eb;
      }

      .cc-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .cc-item-details {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .cc-detail-group {
        display: flex;
        gap: 12px;
        align-items: flex-start;
      }

      .cc-detail-group label {
        font-weight: 600;
        color: #374151;
        min-width: 100px;
        font-size: 14px;
      }

      .cc-detail-group span {
        color: #6b7280;
        font-size: 14px;
      }

      .cc-status-badge {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: 500;
        text-transform: uppercase;
      }

      .cc-status-badge.active {
        background-color: #dcfce7;
        color: #166534;
      }

      .cc-status-badge.draft {
        background-color: #fef3c7;
        color: #92400e;
      }

      .cc-status-badge.completed {
        background-color: #dbeafe;
        color: #1e40af;
      }

      .cc-status-badge.ready {
        background-color: #e0e7ff;
        color: #3730a3;
      }
    `;

    document.head.appendChild(styles);
  }

  // Project Modal
  showCreateProjectModal() {
    const modal = this.createModal('Create New Project', `
      <form class="cc-form" id="create-project-form">
        <div class="cc-form-group">
          <label for="project-name">Project Name *</label>
          <input type="text" id="project-name" required placeholder="Enter project name">
        </div>
        <div class="cc-form-group">
          <label for="project-description">Description</label>
          <textarea id="project-description" rows="3" placeholder="Describe your project..."></textarea>
        </div>
        <div class="cc-form-group">
          <label for="project-priority">Priority</label>
          <select id="project-priority">
            <option value="low">Low</option>
            <option value="medium" selected>Medium</option>
            <option value="high">High</option>
            <option value="urgent">Urgent</option>
          </select>
        </div>
        <div class="cc-form-group">
          <label for="project-tags">Tags (comma-separated)</label>
          <input type="text" id="project-tags" placeholder="web, frontend, react">
        </div>
        <div class="cc-form-actions">
          <button type="button" class="cc-btn cc-btn-secondary">Cancel</button>
          <button type="submit" class="cc-btn cc-btn-primary">Create Project</button>
        </div>
      </form>
    `);

    // Add form submit handler
    const form = modal.querySelector('#create-project-form');
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleCreateProject(form, modal);
    });
  }

  showCreatePersonaModal() {
    const modal = this.createModal('Create New Persona', `
      <form class="cc-form" id="create-persona-form">
        <div class="cc-form-group">
          <label for="persona-name">Persona Name *</label>
          <input type="text" id="persona-name" required placeholder="e.g., Senior Frontend Developer">
        </div>
        <div class="cc-form-group">
          <label for="persona-description">Description</label>
          <textarea id="persona-description" rows="2" placeholder="Brief description of this persona..."></textarea>
        </div>
        <div class="cc-form-group">
          <label for="persona-role">Role</label>
          <select id="persona-role">
            <option value="assistant">Assistant</option>
            <option value="expert">Expert</option>
            <option value="reviewer">Reviewer</option>
            <option value="mentor">Mentor</option>
            <option value="analyst">Analyst</option>
          </select>
        </div>
        <div class="cc-form-group">
          <label for="persona-personality">Personality</label>
          <select id="persona-personality">
            <option value="professional">Professional</option>
            <option value="friendly">Friendly</option>
            <option value="technical">Technical</option>
            <option value="creative">Creative</option>
            <option value="concise">Concise</option>
          </select>
        </div>
        <div class="cc-form-group">
          <label for="persona-expertise">Expertise (comma-separated)</label>
          <input type="text" id="persona-expertise" placeholder="React, TypeScript, Node.js">
        </div>
        <div class="cc-form-group">
          <label for="persona-system-prompt">System Prompt</label>
          <textarea id="persona-system-prompt" rows="3" placeholder="You are a helpful assistant specialized in..."></textarea>
        </div>
        <div class="cc-form-actions">
          <button type="button" class="cc-btn cc-btn-secondary">Cancel</button>
          <button type="submit" class="cc-btn cc-btn-primary">Create Persona</button>
        </div>
      </form>
    `);

    // Add form submit handler
    const form = modal.querySelector('#create-persona-form');
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleCreatePersona(form, modal);
    });
  }

  showCreateArtifactModal() {
    const modal = this.createModal('Create New Artifact', `
      <form class="cc-form" id="create-artifact-form">
        <div class="cc-form-group">
          <label for="artifact-name">Artifact Name *</label>
          <input type="text" id="artifact-name" required placeholder="Enter artifact name">
        </div>
        <div class="cc-form-group">
          <label for="artifact-type">Type</label>
          <select id="artifact-type">
            <option value="document">Document</option>
            <option value="code">Code</option>
            <option value="template">Template</option>
            <option value="specification">Specification</option>
            <option value="guide">Guide</option>
          </select>
        </div>
        <div class="cc-form-group">
          <label for="artifact-content">Content</label>
          <textarea id="artifact-content" rows="6" placeholder="Enter the artifact content..."></textarea>
        </div>
        <div class="cc-form-group">
          <label for="artifact-tags">Tags (comma-separated)</label>
          <input type="text" id="artifact-tags" placeholder="documentation, api, guide">
        </div>
        <div class="cc-form-actions">
          <button type="button" class="cc-btn cc-btn-secondary">Cancel</button>
          <button type="submit" class="cc-btn cc-btn-primary">Create Artifact</button>
        </div>
      </form>
    `);

    // Add form submit handler
    const form = modal.querySelector('#create-artifact-form');
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.handleCreateArtifact(form, modal);
    });
  }

  showExportModal() {
    const modal = this.createModal('Export Data', `
      <div class="cc-form">
        <div class="cc-form-group">
          <label>Export Options</label>
          <div style="display: flex; flex-direction: column; gap: 12px; margin-top: 8px;">
            <label style="display: flex; align-items: center; gap: 8px; font-weight: normal;">
              <input type="checkbox" id="export-projects" checked>
              Export Projects
            </label>
            <label style="display: flex; align-items: center; gap: 8px; font-weight: normal;">
              <input type="checkbox" id="export-personas" checked>
              Export Personas
            </label>
            <label style="display: flex; align-items: center; gap: 8px; font-weight: normal;">
              <input type="checkbox" id="export-artifacts" checked>
              Export Artifacts
            </label>
          </div>
        </div>
        <div class="cc-form-group">
          <label for="export-format">Export Format</label>
          <select id="export-format">
            <option value="json">JSON</option>
            <option value="markdown">Markdown</option>
            <option value="csv">CSV</option>
          </select>
        </div>
        <div class="cc-form-actions">
          <button type="button" class="cc-btn cc-btn-secondary">Cancel</button>
          <button type="button" class="cc-btn cc-btn-primary" id="export-data-btn">Export Data</button>
        </div>
      </div>
    `);
  }

  showItemDetailsModal(itemType, item) {
    const modal = this.createModal(`${itemType.charAt(0).toUpperCase() + itemType.slice(1)}: ${item.name}`, `
      <div class="cc-item-details">
        <div class="cc-detail-group">
          <label>Name:</label>
          <span>${item.name}</span>
        </div>
        <div class="cc-detail-group">
          <label>Status:</label>
          <span class="cc-status-badge ${item.status}">${item.status}</span>
        </div>
        <div class="cc-detail-group">
          <label>Last Modified:</label>
          <span>${item.lastModified}</span>
        </div>
        <div class="cc-detail-group">
          <label>ID:</label>
          <span>${item.id}</span>
        </div>
        <div class="cc-form-actions">
          <button type="button" class="cc-btn cc-btn-secondary">Close</button>
          <button type="button" class="cc-btn cc-btn-primary" id="edit-item-btn">Edit ${itemType.charAt(0).toUpperCase() + itemType.slice(1)}</button>
        </div>
      </div>
    `);
  }

  // Form Handlers
  async handleCreateProject(form, modal) {
    try {
      const formData = {
        name: form.querySelector('#project-name').value,
        description: form.querySelector('#project-description').value,
        priority: form.querySelector('#project-priority').value,
        tags: form.querySelector('#project-tags').value
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0)
      };

      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalText = submitBtn.textContent;
      submitBtn.textContent = 'Creating...';
      submitBtn.disabled = true;

      // Create project
      const project = await this.createProject(formData);

      // Close modal
      modal.remove();

      // Refresh projects list
      this.loadTabData('projects');

      // Show success notification
      this.showNotification('Project created successfully!', 'success');

    } catch (error) {
      console.error('[Coder Companion] Failed to create project:', error);
      this.showNotification('Failed to create project. Please try again.', 'error');

      // Reset button
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Create Project';
      submitBtn.disabled = false;
    }
  }

  async handleCreatePersona(form, modal) {
    try {
      const formData = {
        name: form.querySelector('#persona-name').value,
        description: form.querySelector('#persona-description').value,
        role: form.querySelector('#persona-role').value,
        personality: form.querySelector('#persona-personality').value,
        expertise: form.querySelector('#persona-expertise').value
          .split(',')
          .map(skill => skill.trim())
          .filter(skill => skill.length > 0),
        systemPrompt: form.querySelector('#persona-system-prompt').value
      };

      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Creating...';
      submitBtn.disabled = true;

      // Create persona
      const persona = await this.createPersona(formData);

      // Close modal
      modal.remove();

      // Refresh personas list
      this.loadTabData('personas');

      // Show success notification
      this.showNotification('Persona created successfully!', 'success');

    } catch (error) {
      console.error('[Coder Companion] Failed to create persona:', error);
      this.showNotification('Failed to create persona. Please try again.', 'error');

      // Reset button
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Create Persona';
      submitBtn.disabled = false;
    }
  }

  async handleCreateArtifact(form, modal) {
    try {
      const formData = {
        name: form.querySelector('#artifact-name').value,
        type: form.querySelector('#artifact-type').value,
        content: form.querySelector('#artifact-content').value,
        tags: form.querySelector('#artifact-tags').value
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0)
      };

      // Show loading state
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Creating...';
      submitBtn.disabled = true;

      // Create artifact
      const artifact = await this.createArtifact(formData);

      // Close modal
      modal.remove();

      // Refresh artifacts list
      this.loadTabData('artifacts');

      // Show success notification
      this.showNotification('Artifact created successfully!', 'success');

    } catch (error) {
      console.error('[Coder Companion] Failed to create artifact:', error);
      this.showNotification('Failed to create artifact. Please try again.', 'error');

      // Reset button
      const submitBtn = form.querySelector('button[type="submit"]');
      submitBtn.textContent = 'Create Artifact';
      submitBtn.disabled = false;
    }
  }

  handleExport() {
    const exportProjects = document.getElementById('export-projects')?.checked || false;
    const exportPersonas = document.getElementById('export-personas')?.checked || false;
    const exportArtifacts = document.getElementById('export-artifacts')?.checked || false;
    const format = document.getElementById('export-format')?.value || 'json';

    console.log('[Coder Companion] Exporting data:', {
      projects: exportProjects,
      personas: exportPersonas,
      artifacts: exportArtifacts,
      format: format
    });

    this.performExport({
      projects: exportProjects,
      personas: exportPersonas,
      artifacts: exportArtifacts,
      format: format
    });

    // Close modal
    const modal = document.getElementById('cc-modal');
    if (modal) {
      modal.remove();
    }
  }

  // Data Management
  generateId(prefix = 'item') {
    return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async createProject(data) {
    const projects = await this.getStoredData('projects', []);

    const project = {
      id: this.generateId('proj'),
      name: data.name,
      description: data.description || '',
      priority: data.priority || 'medium',
      tags: data.tags || [],
      status: 'active',
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString()
    };

    projects.push(project);
    await this.setStoredData('projects', projects);

    console.log('[Coder Companion] Created project:', project.name);
    return project;
  }

  async createPersona(data) {
    const personas = await this.getStoredData('personas', []);

    const persona = {
      id: this.generateId('persona'),
      name: data.name,
      description: data.description || '',
      role: data.role || 'assistant',
      personality: data.personality || 'professional',
      expertise: data.expertise || [],
      systemPrompt: data.systemPrompt || '',
      status: 'active',
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      usageCount: 0
    };

    personas.push(persona);
    await this.setStoredData('personas', personas);

    console.log('[Coder Companion] Created persona:', persona.name);
    return persona;
  }

  async createArtifact(data) {
    const artifacts = await this.getStoredData('artifacts', []);

    const artifact = {
      id: this.generateId('artifact'),
      name: data.name,
      type: data.type || 'document',
      content: data.content || '',
      tags: data.tags || [],
      status: 'draft',
      wordCount: this.countWords(data.content || ''),
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString()
    };

    artifacts.push(artifact);
    await this.setStoredData('artifacts', artifacts);

    console.log('[Coder Companion] Created artifact:', artifact.name);
    return artifact;
  }

  countWords(text) {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  async getStoredData(key, defaultValue = null) {
    try {
      console.log(`[Coder Companion] Getting stored data for key: cc_${key}`);
      const data = localStorage.getItem(`cc_${key}`);
      console.log(`[Coder Companion] Raw data from localStorage:`, data);

      if (data) {
        const parsed = JSON.parse(data);
        console.log(`[Coder Companion] Parsed data for ${key}:`, parsed);

        // Ensure we return an array for list data
        if (Array.isArray(parsed)) {
          return parsed;
        } else if (parsed && typeof parsed === 'object') {
          // If it's an object but not an array, wrap it in an array
          return [parsed];
        } else {
          console.warn(`[Coder Companion] Data for ${key} is not an array or object:`, parsed);
          return defaultValue || [];
        }
      } else {
        console.log(`[Coder Companion] No data found for ${key}, returning default:`, defaultValue);
        return defaultValue || [];
      }
    } catch (error) {
      console.error(`[Coder Companion] Failed to get stored data for ${key}:`, error);
      return defaultValue || [];
    }
  }

  async setStoredData(key, value) {
    try {
      localStorage.setItem(`cc_${key}`, JSON.stringify(value));
      console.log(`[Coder Companion] Stored data for ${key}`);
      return true;
    } catch (error) {
      console.error(`[Coder Companion] Failed to store data for ${key}:`, error);
      return false;
    }
  }

  // Update getSampleData to use real data when available
  async getSampleData(tabName) {
    console.log(`[Coder Companion] Getting data for ${tabName}`);
    let realData = [];

    try {
      switch (tabName) {
        case 'projects':
          realData = await this.getStoredData('projects', []);
          console.log(`[Coder Companion] Real projects data:`, realData);
          break;
        case 'personas':
          realData = await this.getStoredData('personas', []);
          console.log(`[Coder Companion] Real personas data:`, realData);
          break;
        case 'artifacts':
          realData = await this.getStoredData('artifacts', []);
          console.log(`[Coder Companion] Real artifacts data:`, realData);
          break;
        case 'export':
          return [
            { id: '1', name: 'Current Project Export', status: 'ready', lastModified: 'Available now' },
            { id: '2', name: 'All Projects Archive', status: 'ready', lastModified: 'Available now' }
          ];
      }
    } catch (error) {
      console.error(`[Coder Companion] Failed to get real data for ${tabName}:`, error);
      realData = [];
    }

    // Ensure realData is an array
    if (!Array.isArray(realData)) {
      console.warn(`[Coder Companion] Real data is not an array for ${tabName}:`, realData);
      realData = [];
    }

    // If we have real data, return it
    if (realData && realData.length > 0) {
      console.log(`[Coder Companion] Returning real data for ${tabName}:`, realData.length, 'items');
      return realData;
    }

    // Otherwise return sample data
    console.log(`[Coder Companion] No real data, returning sample data for ${tabName}`);
    switch (tabName) {
      case 'projects':
        return [
          { id: '1', name: 'AI Assistant Development', status: 'active', lastModified: '2 hours ago' },
          { id: '2', name: 'Code Review Tool', status: 'draft', lastModified: '1 day ago' }
        ];
      case 'personas':
        return [
          { id: '1', name: 'Senior Frontend Developer', status: 'active', lastModified: '30 minutes ago' },
          { id: '2', name: 'Backend Architect', status: 'active', lastModified: '2 hours ago' }
        ];
      case 'artifacts':
        return [
          { id: '1', name: 'API Documentation', status: 'completed', lastModified: '1 hour ago' },
          { id: '2', name: 'Database Schema', status: 'draft', lastModified: '3 hours ago' }
        ];
      default:
        return [];
    }
  }

  // Export functionality
  async performExport(options) {
    try {
      const exportData = {};

      if (options.projects) {
        exportData.projects = await this.getStoredData('projects', []);
      }

      if (options.personas) {
        exportData.personas = await this.getStoredData('personas', []);
      }

      if (options.artifacts) {
        exportData.artifacts = await this.getStoredData('artifacts', []);
      }

      // Add metadata
      exportData.metadata = {
        exportedAt: new Date().toISOString(),
        version: '1.0.0',
        source: 'Coder Companion Extension'
      };

      let content, filename, mimeType;

      switch (options.format) {
        case 'json':
          content = JSON.stringify(exportData, null, 2);
          filename = `coder-companion-export-${new Date().toISOString().split('T')[0]}.json`;
          mimeType = 'application/json';
          break;

        case 'markdown':
          content = this.convertToMarkdown(exportData);
          filename = `coder-companion-export-${new Date().toISOString().split('T')[0]}.md`;
          mimeType = 'text/markdown';
          break;

        case 'csv':
          content = this.convertToCSV(exportData);
          filename = `coder-companion-export-${new Date().toISOString().split('T')[0]}.csv`;
          mimeType = 'text/csv';
          break;

        default:
          throw new Error('Unsupported export format');
      }

      // Create and download file
      this.downloadFile(content, filename, mimeType);
      this.showNotification(`Data exported successfully as ${options.format.toUpperCase()}!`, 'success');

    } catch (error) {
      console.error('[Coder Companion] Export failed:', error);
      this.showNotification('Export failed. Please try again.', 'error');
    }
  }

  convertToMarkdown(data) {
    let markdown = '# Coder Companion Export\n\n';
    markdown += `Exported on: ${new Date().toLocaleDateString()}\n\n`;

    if (data.projects && data.projects.length > 0) {
      markdown += '## Projects\n\n';
      data.projects.forEach(project => {
        markdown += `### ${project.name}\n`;
        markdown += `- **Status**: ${project.status}\n`;
        markdown += `- **Priority**: ${project.priority}\n`;
        markdown += `- **Description**: ${project.description || 'No description'}\n`;
        markdown += `- **Tags**: ${project.tags.join(', ') || 'None'}\n`;
        markdown += `- **Created**: ${new Date(project.createdAt).toLocaleDateString()}\n\n`;
      });
    }

    if (data.personas && data.personas.length > 0) {
      markdown += '## Personas\n\n';
      data.personas.forEach(persona => {
        markdown += `### ${persona.name}\n`;
        markdown += `- **Role**: ${persona.role}\n`;
        markdown += `- **Personality**: ${persona.personality}\n`;
        markdown += `- **Description**: ${persona.description || 'No description'}\n`;
        markdown += `- **Expertise**: ${persona.expertise.join(', ') || 'None'}\n`;
        markdown += `- **System Prompt**: ${persona.systemPrompt || 'None'}\n\n`;
      });
    }

    if (data.artifacts && data.artifacts.length > 0) {
      markdown += '## Artifacts\n\n';
      data.artifacts.forEach(artifact => {
        markdown += `### ${artifact.name}\n`;
        markdown += `- **Type**: ${artifact.type}\n`;
        markdown += `- **Status**: ${artifact.status}\n`;
        markdown += `- **Word Count**: ${artifact.wordCount}\n`;
        markdown += `- **Tags**: ${artifact.tags.join(', ') || 'None'}\n`;
        markdown += `- **Content**:\n\`\`\`\n${artifact.content}\n\`\`\`\n\n`;
      });
    }

    return markdown;
  }

  convertToCSV(data) {
    let csv = '';

    if (data.projects && data.projects.length > 0) {
      csv += 'Type,Name,Status,Priority,Description,Tags,Created\n';
      data.projects.forEach(project => {
        csv += `Project,"${project.name}","${project.status}","${project.priority}","${project.description || ''}","${project.tags.join('; ')}","${project.createdAt}"\n`;
      });
      csv += '\n';
    }

    if (data.personas && data.personas.length > 0) {
      csv += 'Type,Name,Role,Personality,Description,Expertise,Created\n';
      data.personas.forEach(persona => {
        csv += `Persona,"${persona.name}","${persona.role}","${persona.personality}","${persona.description || ''}","${persona.expertise.join('; ')}","${persona.createdAt}"\n`;
      });
      csv += '\n';
    }

    if (data.artifacts && data.artifacts.length > 0) {
      csv += 'Type,Name,ArtifactType,Status,WordCount,Tags,Created\n';
      data.artifacts.forEach(artifact => {
        csv += `Artifact,"${artifact.name}","${artifact.type}","${artifact.status}","${artifact.wordCount}","${artifact.tags.join('; ')}","${artifact.createdAt}"\n`;
      });
    }

    return csv;
  }

  downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    a.style.display = 'none';

    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    URL.revokeObjectURL(url);
  }

  // Notification system
  showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.cc-notification');
    existingNotifications.forEach(notification => notification.remove());

    const notification = document.createElement('div');
    notification.className = `cc-notification cc-notification-${type}`;
    notification.innerHTML = `
      <div class="cc-notification-content">
        <span class="cc-notification-message">${message}</span>
        <button class="cc-notification-close" onclick="this.parentElement.parentElement.remove()">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
    `;

    // Add notification styles if not already added
    this.addNotificationStyles();

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 5000);

    return notification;
  }

  addNotificationStyles() {
    if (document.getElementById('cc-notification-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'cc-notification-styles';
    styles.textContent = `
      .cc-notification {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10001;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        border-left: 4px solid;
        animation: slideInRight 0.3s ease-out;
        max-width: 400px;
      }

      .cc-notification-success {
        border-left-color: #10b981;
      }

      .cc-notification-error {
        border-left-color: #ef4444;
      }

      .cc-notification-info {
        border-left-color: #3b82f6;
      }

      .cc-notification-warning {
        border-left-color: #f59e0b;
      }

      .cc-notification-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        gap: 12px;
      }

      .cc-notification-message {
        font-size: 14px;
        color: #374151;
        line-height: 1.4;
      }

      .cc-notification-close {
        background: none;
        border: none;
        padding: 4px;
        cursor: pointer;
        color: #9ca3af;
        border-radius: 4px;
        transition: all 0.2s ease;
        flex-shrink: 0;
      }

      .cc-notification-close:hover {
        background: #f3f4f6;
        color: #6b7280;
      }

      @keyframes slideInRight {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
    `;

    document.head.appendChild(styles);
  }

  initializeSidebarComponents() {
    console.log('[Coder Companion] Initializing sidebar components...');

    // Wait for sidebar content to be fully loaded
    const checkSidebarReady = () => {
      const sidebarDocument = this.sidebarContainer.contentDocument;
      if (sidebarDocument && sidebarDocument.readyState === 'complete') {
        // Sidebar is ready, we can now interact with it
        this.setupSidebarCommunication();
      } else {
        // Check again in a moment
        setTimeout(checkSidebarReady, 50);
      }
    };

    checkSidebarReady();
  }

  setupMessageListener() {
    console.log('[Coder Companion] Setting up message listener...');

    // Listen for messages from the popup
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      console.log('[Coder Companion] Received message:', message);

      if (message.type === 'PING') {
        console.log('[Coder Companion] Received PING, responding with PONG');
        const response = { type: 'PONG', isReady: this.isInitialized && this.isInjected };
        console.log('[Coder Companion] PING response:', response);
        sendResponse(response);
        return true;
      }

      if (message.type === 'OPEN_SIDEBAR') {
        console.log('[Coder Companion] Received OPEN_SIDEBAR message');
        console.log('[Coder Companion] Extension state:', {
          isInitialized: this.isInitialized,
          isInjected: this.isInjected,
          sidebarContainer: !!this.sidebarContainer,
          isSidebarOpen: this.isSidebarOpen
        });

        // Ensure extension is fully initialized before opening sidebar
        if (!this.isInitialized || !this.isInjected) {
          console.warn('[Coder Companion] Extension not fully initialized, waiting...');
          // Wait for initialization to complete
          const checkReady = () => {
            if (this.isInitialized && this.isInjected) {
              try {
                this.openSidebar();
                console.log('[Coder Companion] Sidebar opened successfully');
                const response = { success: true };
                console.log('[Coder Companion] OPEN_SIDEBAR response:', response);
                sendResponse(response);
              } catch (error) {
                console.error('[Coder Companion] Failed to open sidebar:', error);
                const response = { success: false, error: error.message };
                console.log('[Coder Companion] OPEN_SIDEBAR error response:', response);
                sendResponse(response);
              }
            } else {
              setTimeout(checkReady, 100);
            }
          };
          checkReady();
        } else {
          try {
            this.openSidebar();
            console.log('[Coder Companion] Sidebar opened successfully');
            const response = { success: true };
            console.log('[Coder Companion] OPEN_SIDEBAR response:', response);
            sendResponse(response);
          } catch (error) {
            console.error('[Coder Companion] Failed to open sidebar:', error);
            const response = { success: false, error: error.message };
            console.log('[Coder Companion] OPEN_SIDEBAR error response:', response);
            sendResponse(response);
          }
        }
      }
      return true; // Keep the message channel open for async response
    });

    console.log('[Coder Companion] Message listener set up successfully');
  }

  setupSidebarCommunication() {
    console.log('[Coder Companion] Setting up sidebar communication...');

    // Listen for messages from the sidebar
    const sidebarWindow = this.sidebarContainer.contentWindow;

    if (sidebarWindow) {
      // Handle sidebar close events
      sidebarWindow.addEventListener('message', (event) => {
        if (event.data.type === 'SIDEBAR_CLOSE') {
          this.closeSidebar();
        }
      });

      // Send initialization message to sidebar
      sidebarWindow.postMessage({
        type: 'SIDEBAR_INIT',
        data: {
          url: window.location.href,
          timestamp: Date.now()
        }
      }, '*');
    }
  }

  saveExtensionState() {
    const state = {
      sidebarOpen: this.isSidebarOpen,
      lastActiveTab: 'projects' // Default tab
    };

    chrome.storage.local.set(state);
  }
}

// Initialize when script loads
console.log('[Coder Companion] Content script loaded');
new CoderCompanionInjector().init();

// Save state on page unload
window.addEventListener('beforeunload', () => {
  // Note: This is a placeholder - actual implementation will be in the injector instance
  console.log('[Coder Companion] Saving extension state...');
});