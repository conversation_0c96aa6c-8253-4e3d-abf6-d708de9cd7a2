/* extension/styles/extension.css - Main styles for Coder Companion extension */

/* CSS Reset and Base Styles */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
}

/* Extension Isolation */
#cc-sidebar,
#cc-toggle-btn,
.cc-extension {
  all: initial;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

/* Toggle Button Styles */
#cc-toggle-btn {
  position: fixed !important;
  top: 16px !important;
  right: 16px !important;
  z-index: 10000 !important;
  width: 48px !important;
  height: 48px !important;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8) !important;
  color: white !important;
  border: none !important;
  border-radius: 50% !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
  transition: all 0.3s ease !important;
  font-size: 20px !important;
}

#cc-toggle-btn:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4) !important;
  background: linear-gradient(135deg, #2563eb, #1e40af) !important;
}

#cc-toggle-btn:active {
  transform: scale(0.95) !important;
}

/* Ensure SVG icons don't interfere with button clicks */
#cc-toggle-btn svg,
#cc-toggle-btn svg * {
  pointer-events: none !important;
}

/* Sidebar Container */
#cc-sidebar {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  width: 384px !important;
  height: 100vh !important;
  background: white !important;
  border-left: 1px solid #e5e7eb !important;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1) !important;
  z-index: 9999 !important;
  transform: translateX(100%) !important;
  transition: transform 0.3s ease !important;
  display: flex !important;
  flex-direction: column !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  overflow: hidden !important;
}

#cc-sidebar.open {
  transform: translateX(0) !important;
}

/* Sidebar Header */
.sidebar-header {
  padding: 16px 20px !important;
  border-bottom: 1px solid #e5e7eb !important;
  background: #f9fafb !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  flex-shrink: 0 !important;
}

.sidebar-header h2 {
  margin: 0 !important;
  font-size: 18px !important;
  font-weight: 600 !important;
  color: #111827 !important;
}

.sidebar-close {
  background: none !important;
  border: none !important;
  color: #6b7280 !important;
  cursor: pointer !important;
  padding: 8px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

.sidebar-close:hover {
  background: #e5e7eb !important;
  color: #374151 !important;
}

/* Sidebar Content */
.sidebar-content {
  flex: 1 !important;
  overflow-y: auto !important;
  padding: 0 !important;
}

/* Tab Navigation */
.sidebar-tabs {
  display: flex !important;
  background: #f9fafb !important;
  border-bottom: 1px solid #e5e7eb !important;
}

.tab-button {
  flex: 1 !important;
  padding: 12px 16px !important;
  background: none !important;
  border: none !important;
  color: #6b7280 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

.tab-button.active {
  background: white !important;
  color: #3b82f6 !important;
  border-bottom: 2px solid #3b82f6 !important;
}

.tab-button:hover:not(.active) {
  background: #e5e7eb !important;
  color: #374151 !important;
}

/* Tab Content */
.tab-content {
  padding: 20px !important;
  display: none !important;
}

.tab-content.active {
  display: block !important;
}

/* Form Elements */
.cc-input {
  width: 100% !important;
  padding: 10px 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  transition: border-color 0.2s ease !important;
  margin-bottom: 12px !important;
}

.cc-input:focus {
  outline: none !important;
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.cc-button {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 10px 16px !important;
  background: #3b82f6 !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  text-decoration: none !important;
}

.cc-button:hover {
  background: #2563eb !important;
  transform: translateY(-1px) !important;
}

.cc-button.secondary {
  background: #6b7280 !important;
}

.cc-button.secondary:hover {
  background: #4b5563 !important;
}

.cc-button.danger {
  background: #dc2626 !important;
}

.cc-button.danger:hover {
  background: #b91c1c !important;
}

/* Lists */
.cc-list {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.cc-list-item {
  padding: 12px 16px !important;
  border-bottom: 1px solid #f3f4f6 !important;
  cursor: pointer !important;
  transition: background-color 0.2s ease !important;
}

.cc-list-item:hover {
  background: #f9fafb !important;
}

.cc-list-item:last-child {
  border-bottom: none !important;
}

/* Cards */
.cc-card {
  background: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  padding: 16px !important;
  margin-bottom: 12px !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* Loading States */
.cc-loading {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 20px !important;
  color: #6b7280 !important;
  font-size: 14px !important;
}

.spinner {
  width: 20px !important;
  height: 20px !important;
  border: 2px solid #e5e7eb !important;
  border-top: 2px solid #3b82f6 !important;
  border-radius: 50% !important;
  animation: spin 1s linear infinite !important;
  margin-right: 8px !important;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 640px) {
  #cc-sidebar {
    width: 100vw !important;
  }

  #cc-toggle-btn {
    top: 12px !important;
    right: 12px !important;
    width: 44px !important;
    height: 44px !important;
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  #cc-sidebar {
    background: #1f2937 !important;
    border-left-color: #374151 !important;
  }

  .sidebar-header {
    background: #111827 !important;
    border-bottom-color: #374151 !important;
  }

  .sidebar-header h2 {
    color: #f9fafb !important;
  }

  .sidebar-close {
    color: #9ca3af !important;
  }

  .sidebar-close:hover {
    background: #374151 !important;
    color: #f3f4f6 !important;
  }

  .sidebar-tabs {
    background: #111827 !important;
    border-bottom-color: #374151 !important;
  }

  .tab-button {
    color: #9ca3af !important;
  }

  .tab-button.active {
    background: #1f2937 !important;
    color: #3b82f6 !important;
  }

  .tab-button:hover:not(.active) {
    background: #374151 !important;
    color: #f3f4f6 !important;
  }

  .cc-input {
    background: #1f2937 !important;
    border-color: #374151 !important;
    color: #f9fafb !important;
  }

  .cc-input:focus {
    border-color: #3b82f6 !important;
  }

  .cc-list-item:hover {
    background: #111827 !important;
  }

  .cc-card {
    background: #1f2937 !important;
    border-color: #374151 !important;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  #cc-toggle-btn,
  #cc-sidebar,
  .cc-button,
  .tab-button,
  .sidebar-close {
    transition: none !important;
  }

  .spinner {
    animation: none !important;
  }
}

/* Focus styles for keyboard navigation */
#cc-toggle-btn:focus,
.cc-button:focus,
.cc-input:focus,
.tab-button:focus,
.sidebar-close:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
}

/* High contrast mode */
@media (prefers-contrast: high) {
  #cc-sidebar {
    border-left-width: 2px !important;
  }

  .cc-input {
    border-width: 2px !important;
  }

  .cc-button {
    border: 2px solid transparent !important;
  }

  .cc-card {
    border-width: 2px !important;
  }
}