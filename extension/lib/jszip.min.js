// extension/lib/jszip.min.js - ZIP creation utility
// Simple mock implementation for ZIP functionality

class JSZip {
  constructor() {
    this.files = {};
  }

  file(name, content) {
    this.files[name] = content;
    return this;
  }

  generateAsync(options = {}) {
    return new Promise((resolve) => {
      // Create a mock ZIP file content
      let zipContent = 'Mock ZIP Archive\n';
      zipContent += `Created: ${new Date().toISOString()}\n`;
      zipContent += `Files: ${Object.keys(this.files).length}\n\n`;

      zipContent += 'Contents:\n';
      for (const [name, content] of Object.entries(this.files)) {
        zipContent += `\n--- ${name} ---\n`;
        zipContent += content + '\n';
      }

      const blob = new Blob([zipContent], {
        type: options.type || 'application/zip'
      });

      resolve(blob);
    });
  }
}

// Export for use in modules
window.JSZip = JSZip;