// Minimal sidebar script for debugging
console.log('[Sidebar-Minimal] Script loaded');

class MinimalSidebarManager {
  constructor() {
    console.log('[Sidebar-Minimal] Constructor called');
    this.currentTab = 'projects';
    this.init();
  }

  async init() {
    console.log('[Sidebar-Minimal] Initializing...');
    
    // Wait for DOM
    await this.waitForDOM();
    
    // Create simple content
    this.createSimpleContent();
    
    // Setup basic event listeners
    this.setupEventListeners();
    
    console.log('[Sidebar-Minimal] Initialization complete');
  }

  async waitForDOM() {
    return new Promise((resolve) => {
      if (document.readyState === 'complete') {
        resolve();
      } else {
        window.addEventListener('load', resolve);
        // Also try after a short delay
        setTimeout(resolve, 100);
      }
    });
  }

  createSimpleContent() {
    console.log('[Sidebar-Minimal] Creating simple content...');
    
    const tabs = ['projects', 'personas', 'artifacts', 'export'];
    
    tabs.forEach(tabName => {
      const tabElement = document.getElementById(`${tabName}-tab`);
      console.log(`[Sidebar-Minimal] Tab ${tabName}:`, tabElement);
      
      if (tabElement) {
        tabElement.innerHTML = `
          <div style="padding: 20px;">
            <h3>${tabName.charAt(0).toUpperCase() + tabName.slice(1)}</h3>
            <p>This is the ${tabName} tab content.</p>
            <button id="${tabName}-test-btn" style="padding: 8px 16px; background: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer;">
              Test ${tabName}
            </button>
            <div id="${tabName}-content" style="margin-top: 16px; padding: 12px; background: #f3f4f6; border-radius: 4px;">
              Content will appear here...
            </div>
          </div>
        `;
        console.log(`[Sidebar-Minimal] Created content for ${tabName}`);
      } else {
        console.error(`[Sidebar-Minimal] Tab element not found: ${tabName}-tab`);
      }
    });
  }

  setupEventListeners() {
    console.log('[Sidebar-Minimal] Setting up event listeners...');
    
    // Tab navigation
    document.querySelectorAll('.tab-button').forEach(button => {
      button.addEventListener('click', () => {
        const tabId = button.dataset.tab;
        console.log(`[Sidebar-Minimal] Tab clicked: ${tabId}`);
        this.switchTab(tabId);
      });
    });

    // Test buttons
    const tabs = ['projects', 'personas', 'artifacts', 'export'];
    tabs.forEach(tabName => {
      const button = document.getElementById(`${tabName}-test-btn`);
      if (button) {
        button.addEventListener('click', () => {
          console.log(`[Sidebar-Minimal] Test button clicked: ${tabName}`);
          this.handleTestClick(tabName);
        });
      }
    });
  }

  switchTab(tabId) {
    console.log(`[Sidebar-Minimal] Switching to tab: ${tabId}`);
    
    // Update tab buttons
    document.querySelectorAll('.tab-button').forEach(button => {
      button.classList.remove('active');
    });
    const activeButton = document.querySelector(`[data-tab="${tabId}"]`);
    if (activeButton) {
      activeButton.classList.add('active');
    }

    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    const activeContent = document.getElementById(`${tabId}-tab`);
    if (activeContent) {
      activeContent.classList.add('active');
    }

    this.currentTab = tabId;
  }

  handleTestClick(tabName) {
    console.log(`[Sidebar-Minimal] Handling test click for: ${tabName}`);
    
    const contentDiv = document.getElementById(`${tabName}-content`);
    if (contentDiv) {
      const timestamp = new Date().toLocaleTimeString();
      contentDiv.innerHTML = `
        <strong>Test executed at ${timestamp}</strong><br>
        Tab: ${tabName}<br>
        Status: Working ✅<br>
        <small>This confirms that the ${tabName} tab is functional.</small>
      `;
    }

    // Test localStorage
    try {
      const key = `test_${tabName}`;
      const value = { timestamp, tab: tabName, test: 'success' };
      localStorage.setItem(key, JSON.stringify(value));
      console.log(`[Sidebar-Minimal] Saved to localStorage: ${key}`);
    } catch (error) {
      console.error(`[Sidebar-Minimal] localStorage error:`, error);
    }
  }
}

// Initialize when ready
function initializeMinimalSidebar() {
  console.log('[Sidebar-Minimal] Initializing minimal sidebar...');
  console.log('[Sidebar-Minimal] Document ready state:', document.readyState);
  
  // Check if required elements exist
  const requiredElements = ['projects-tab', 'personas-tab', 'artifacts-tab', 'export-tab'];
  const elementsStatus = {};
  
  requiredElements.forEach(id => {
    const element = document.getElementById(id);
    elementsStatus[id] = !!element;
    console.log(`[Sidebar-Minimal] Element ${id}:`, element ? 'Found' : 'Not found');
  });

  if (Object.values(elementsStatus).some(exists => exists)) {
    console.log('[Sidebar-Minimal] At least some elements found, proceeding...');
    window.minimalSidebarManager = new MinimalSidebarManager();
  } else {
    console.error('[Sidebar-Minimal] No required elements found, retrying in 500ms...');
    setTimeout(initializeMinimalSidebar, 500);
  }
}

// Multiple initialization attempts
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeMinimalSidebar);
} else {
  initializeMinimalSidebar();
}

// Fallback initialization
setTimeout(() => {
  if (!window.minimalSidebarManager) {
    console.log('[Sidebar-Minimal] Fallback initialization...');
    initializeMinimalSidebar();
  }
}, 1000);

console.log('[Sidebar-Minimal] Script setup complete');
