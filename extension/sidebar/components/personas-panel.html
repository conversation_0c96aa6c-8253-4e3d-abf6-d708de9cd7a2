<!DOCTYPE html>
<!-- extension/sidebar/components/personas-panel.html -->
<div id="personas-panel" class="panel personas-panel">
  <div class="panel-header">
    <div class="panel-actions">
      <button id="new-persona-btn" class="cc-button primary">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="12" y1="5" x2="12" y2="19"/>
          <line x1="5" y1="12" x2="19" y2="12"/>
        </svg>
        New Persona
      </button>
      <button id="refresh-personas-btn" class="cc-button secondary">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="23,4 23,10 17,10"/>
          <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
        </svg>
      </button>
    </div>
    <div class="search-container">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="11" cy="11" r="8"/>
        <path d="M21 21l-4.35-4.35"/>
      </svg>
      <input type="text" id="persona-search" class="cc-input" placeholder="Search personas...">
    </div>
  </div>

  <div class="panel-content">
    <!-- Active Persona Display -->
    <div class="active-persona-section">
      <div class="section-header">
        <h4>Active Persona</h4>
      </div>
      <div id="active-persona-display" class="active-persona-display">
        <div class="no-active-persona">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
            <circle cx="12" cy="7" r="4"/>
          </svg>
          <p>No active persona selected</p>
        </div>
      </div>
    </div>

    <!-- Personas List -->
    <div class="section-header">
      <h4>All Personas</h4>
    </div>
    <div id="personas-list" class="cc-list">
      <div class="cc-loading">
        <div class="spinner"></div>
        Loading personas...
      </div>
    </div>
  </div>

  <!-- Persona Statistics -->
  <div class="panel-footer">
    <div class="stats-bar">
      <div class="stat-item">
        <span class="stat-label">Total:</span>
        <span class="stat-value" id="personas-total">0</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Active:</span>
        <span class="stat-value" id="personas-active">0</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Usage:</span>
        <span class="stat-value" id="personas-usage">0</span>
      </div>
    </div>
  </div>
</div>