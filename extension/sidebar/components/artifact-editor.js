// extension/sidebar/components/artifact-editor.js - Artifact editor functionality

class ArtifactEditorComponent {
  constructor(container) {
    this.container = container;
    this.currentArtifact = null;
    this.currentMode = 'split'; // 'edit', 'preview', 'split'
    this.autoSaveController = null;
    this.searchMatches = [];
    this.currentSearchIndex = -1;

    this.init();
  }

  async init() {
    console.log('[ArtifactEditor] Initializing component...');

    this.setupEventListeners();
    this.setupToolbar();
    this.setupResizer();
    this.setupSearch();

    // Load modules
    await this.loadModules();

    // Create empty artifact for demo
    await this.createNewArtifact();

    console.log('[ArtifactEditor] Component initialized');
  }

  async loadModules() {
    // In a real implementation, these would be imported properly
    // For now, we'll use the global instances
    this.artifactEditor = window.artifactEditor;
    this.projectManager = window.projectManager;
    this.personaWorkflow = window.personaWorkflow;
  }

  setupEventListeners() {
    // Save button
    const saveBtn = this.container.querySelector('#editor-save-btn');
    if (saveBtn) {
      saveBtn.addEventListener('click', () => this.saveArtifact());
    }

    // Preview button
    const previewBtn = this.container.querySelector('#editor-preview-btn');
    if (previewBtn) {
      previewBtn.addEventListener('click', () => this.togglePreview());
    }

    // Mode toggle buttons
    const editModeBtn = this.container.querySelector('#edit-mode-btn');
    const previewModeBtn = this.container.querySelector('#preview-mode-btn');
    const splitModeBtn = this.container.querySelector('#split-mode-btn');

    editModeBtn?.addEventListener('click', () => this.setMode('edit'));
    previewModeBtn?.addEventListener('click', () => this.setMode('preview'));
    splitModeBtn?.addEventListener('click', () => this.setMode('split'));

    // Editor input
    const editor = this.container.querySelector('#markdown-editor');
    if (editor) {
      editor.addEventListener('input', () => this.handleEditorInput());
      editor.addEventListener('keydown', (e) => this.handleKeydown(e));
    }
  }

  setupToolbar() {
    const toolbarBtns = this.container.querySelectorAll('.toolbar-btn');
    toolbarBtns.forEach(btn => {
      btn.addEventListener('click', () => {
        const command = btn.dataset.command;
        this.executeCommand(command);
      });
    });
  }

  setupResizer() {
    const resizer = this.container.querySelector('#editor-resizer');
    const container = this.container.querySelector('.editor-container');
    let isResizing = false;
    let startX = 0;
    let startWidth = 0;

    if (resizer && container) {
      resizer.addEventListener('mousedown', (e) => {
        isResizing = true;
        startX = e.clientX;
        startWidth = this.container.querySelector('.editor-pane').offsetWidth;
        document.body.style.cursor = 'col-resize';
      });

      document.addEventListener('mousemove', (e) => {
        if (!isResizing) return;

        const deltaX = e.clientX - startX;
        const newWidth = Math.max(200, startWidth + deltaX);
        const containerWidth = container.offsetWidth;

        if (newWidth < containerWidth - 200) {
          this.container.querySelector('.editor-pane').style.flex = `0 0 ${newWidth}px`;
        }
      });

      document.addEventListener('mouseup', () => {
        if (isResizing) {
          isResizing = false;
          document.body.style.cursor = '';
          this.container.querySelector('.editor-pane').style.flex = '';
        }
      });
    }
  }

  setupSearch() {
    const searchInput = this.container.querySelector('#editor-search-input');
    const searchPrev = this.container.querySelector('#editor-search-prev');
    const searchNext = this.container.querySelector('#editor-search-next');
    const searchClose = this.container.querySelector('#editor-search-close');

    searchInput?.addEventListener('input', (e) => {
      this.performSearch(e.target.value);
    });

    searchPrev?.addEventListener('click', () => this.navigateSearch(-1));
    searchNext?.addEventListener('click', () => this.navigateSearch(1));
    searchClose?.addEventListener('click', () => this.clearSearch());
  }

  async createNewArtifact() {
    const editor = this.container.querySelector('#markdown-editor');
    const titleElement = this.container.querySelector('#editor-title');

    if (editor && titleElement) {
      editor.value = '# Welcome to Coder Companion\n\nStart writing your artifact here...';
      titleElement.textContent = 'Untitled Artifact';
      this.updatePreview();
      this.updateWordCount();
    }
  }

  async saveArtifact() {
    const editor = this.container.querySelector('#markdown-editor');
    const titleElement = this.container.querySelector('#editor-title');

    if (!editor || !titleElement) return;

    const content = editor.value;
    const title = titleElement.textContent;

    try {
      // In a real implementation, this would save to the artifact editor module
      console.log('[ArtifactEditor] Saving artifact:', title);

      // Update last saved time
      this.updateLastSavedTime();

      // Show success feedback
      this.showNotification('Artifact saved successfully!', 'success');

    } catch (error) {
      console.error('[ArtifactEditor] Save failed:', error);
      this.showNotification('Failed to save artifact', 'error');
    }
  }

  handleEditorInput() {
    this.updatePreview();
    this.updateWordCount();

    // Trigger auto-save if enabled
    if (this.autoSaveController) {
      this.autoSaveController.save(this.container.querySelector('#markdown-editor').value);
    }
  }

  handleKeydown(event) {
    // Handle keyboard shortcuts
    if (event.ctrlKey || event.metaKey) {
      switch (event.key) {
        case 's':
          event.preventDefault();
          this.saveArtifact();
          break;
        case 'b':
          event.preventDefault();
          this.executeCommand('bold');
          break;
        case 'i':
          event.preventDefault();
          this.executeCommand('italic');
          break;
        case '`':
          event.preventDefault();
          this.executeCommand('code');
          break;
      }
    }
  }

  executeCommand(command) {
    const editor = this.container.querySelector('#markdown-editor');
    if (!editor) return;

    const start = editor.selectionStart;
    const end = editor.selectionEnd;
    const selectedText = editor.value.substring(start, end);
    const beforeText = editor.value.substring(0, start);
    const afterText = editor.value.substring(end);

    let newText = '';
    let newStart = start;
    let newEnd = end;

    switch (command) {
      case 'bold':
        newText = `**${selectedText || 'bold text'}**`;
        newStart = start + 2;
        newEnd = end + 2;
        break;
      case 'italic':
        newText = `*${selectedText || 'italic text'}*`;
        newStart = start + 1;
        newEnd = end + 1;
        break;
      case 'code':
        newText = `\`${selectedText || 'code'}\``;
        newStart = start + 1;
        newEnd = end + 1;
        break;
      case 'h1':
        newText = `# ${selectedText || 'Heading 1'}`;
        break;
      case 'h2':
        newText = `## ${selectedText || 'Heading 2'}`;
        break;
      case 'h3':
        newText = `### ${selectedText || 'Heading 3'}`;
        break;
      case 'ul':
        newText = `- ${selectedText || 'List item'}`;
        break;
      case 'ol':
        newText = `1. ${selectedText || 'List item'}`;
        break;
      case 'quote':
        newText = `> ${selectedText || 'Quote'}`;
        break;
      case 'link':
        newText = `[${selectedText || 'link text'}](url)`;
        newStart = start + (selectedText ? selectedText.length + 3 : 13);
        break;
      case 'image':
        newText = `![${selectedText || 'alt text'}](image-url)`;
        newStart = start + (selectedText ? selectedText.length + 3 : 20);
        break;
      case 'codeblock':
        newText = `\`\`\`\n${selectedText || 'code here'}\n\`\`\``;
        newStart = start + 4;
        newEnd = end + 4;
        break;
      case 'undo':
        document.execCommand('undo');
        return;
      case 'redo':
        document.execCommand('redo');
        return;
    }

    if (newText) {
      editor.setRangeText(newText, start, end, 'end');
      editor.focus();
      editor.setSelectionRange(newStart, newEnd);

      this.handleEditorInput();
    }
  }

  updatePreview() {
    const editor = this.container.querySelector('#markdown-editor');
    const preview = this.container.querySelector('#markdown-preview');

    if (editor && preview) {
      const markdownText = editor.value;
      preview.innerHTML = this.renderMarkdown(markdownText);
    }
  }

  renderMarkdown(text) {
    // Basic markdown rendering (in production, use marked.js)
    let html = text;

    // Headers
    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

    // Bold and Italic
    html = html.replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em>$1</em></strong>');
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Code blocks
    html = html.replace(/```(\w+)?\n([\s\S]*?)\n```/g, '<pre><code class="language-$1">$2</code></pre>');

    // Inline code
    html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

    // Lists
    html = html.replace(/^\d+\. (.*$)/gim, '<li>$1</li>');
    html = html.replace(/^- (.*$)/gim, '<li>$1</li>');

    // Blockquotes
    html = html.replace(/^> (.*$)/gim, '<blockquote>$1</blockquote>');

    // Links and images
    html = html.replace(/!\[(.*?)\]\((.*?)\)/g, '<img src="$2" alt="$1">');
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

    // Line breaks
    html = html.replace(/\n/g, '<br>');

    return html;
  }

  updateWordCount() {
    const editor = this.container.querySelector('#markdown-editor');
    const wordCountElement = this.container.querySelector('#editor-word-count');

    if (editor && wordCountElement) {
      const text = editor.value;
      const words = text.trim().split(/\s+/).filter(word => word.length > 0);
      wordCountElement.textContent = `${words.length} words`;
    }
  }

  updateLastSavedTime() {
    const lastSavedElement = this.container.querySelector('#editor-last-saved');
    if (lastSavedElement) {
      const now = new Date();
      lastSavedElement.textContent = `Last saved: ${now.toLocaleTimeString()}`;
    }
  }

  togglePreview() {
    const previewPane = this.container.querySelector('.preview-pane');
    const isVisible = previewPane.style.display !== 'none';

    if (isVisible) {
      this.setMode('edit');
    } else {
      this.setMode('preview');
    }
  }

  setMode(mode) {
    const container = this.container.querySelector('.editor-container');
    const editBtn = this.container.querySelector('#edit-mode-btn');
    const previewBtn = this.container.querySelector('#preview-mode-btn');
    const splitBtn = this.container.querySelector('#split-mode-btn');

    // Reset classes
    container.className = 'editor-container';

    // Update button states
    editBtn.classList.remove('active');
    previewBtn.classList.remove('active');
    splitBtn.classList.remove('active');

    switch (mode) {
      case 'edit':
        container.classList.add('edit-only');
        editBtn.classList.add('active');
        break;
      case 'preview':
        container.classList.add('preview-only');
        previewBtn.classList.add('active');
        break;
      case 'split':
      default:
        // Split mode is default
        splitBtn.classList.add('active');
        break;
    }

    this.currentMode = mode;
  }

  performSearch(query) {
    if (!query.trim()) {
      this.clearSearch();
      return;
    }

    const editor = this.container.querySelector('#markdown-editor');
    if (!editor) return;

    const text = editor.value;
    const regex = new RegExp(query, 'gi');
    this.searchMatches = [];
    let match;

    while ((match = regex.exec(text)) !== null) {
      this.searchMatches.push({
        index: match.index,
        length: match[0].length,
        text: match[0]
      });
    }

    this.currentSearchIndex = this.searchMatches.length > 0 ? 0 : -1;
    this.highlightCurrentSearchMatch();
    this.updateSearchUI();
  }

  navigateSearch(direction) {
    if (this.searchMatches.length === 0) return;

    this.currentSearchIndex += direction;

    if (this.currentSearchIndex < 0) {
      this.currentSearchIndex = this.searchMatches.length - 1;
    } else if (this.currentSearchIndex >= this.searchMatches.length) {
      this.currentSearchIndex = 0;
    }

    this.highlightCurrentSearchMatch();
    this.updateSearchUI();
  }

  highlightCurrentSearchMatch() {
    const editor = this.container.querySelector('#markdown-editor');
    if (!editor || this.currentSearchIndex === -1) return;

    const match = this.searchMatches[this.currentSearchIndex];
    editor.focus();
    editor.setSelectionRange(match.index, match.index + match.length);

    // Scroll to selection
    const textArea = editor;
    const lineHeight = parseInt(getComputedStyle(textArea).lineHeight);
    const lines = textArea.value.substring(0, match.index).split('\n').length - 1;
    textArea.scrollTop = lines * lineHeight;
  }

  updateSearchUI() {
    const searchInput = this.container.querySelector('#editor-search-input');
    const searchPrev = this.container.querySelector('#editor-search-prev');
    const searchNext = this.container.querySelector('#editor-search-next');

    if (this.searchMatches.length > 0) {
      searchInput.style.borderColor = '#10b981';
      searchPrev.disabled = false;
      searchNext.disabled = false;
    } else {
      searchInput.style.borderColor = '#ef4444';
      searchPrev.disabled = true;
      searchNext.disabled = true;
    }
  }

  clearSearch() {
    const searchInput = this.container.querySelector('#editor-search-input');
    if (searchInput) {
      searchInput.value = '';
    }

    this.searchMatches = [];
    this.currentSearchIndex = -1;
    this.updateSearchUI();
  }

  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
      color: white;
      padding: 12px 16px;
      border-radius: 6px;
      z-index: 10000;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      animation: slideInRight 0.3s ease;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.animation = 'slideOutRight 0.3s ease';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }
}

// CSS for notifications and animations
const style = document.createElement('style');
style.textContent = `
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
`;
document.head.appendChild(style);

// Initialize when component is loaded
document.addEventListener('DOMContentLoaded', () => {
  const container = document.getElementById('artifact-editor-component');
  if (container) {
    window.artifactEditorComponent = new ArtifactEditorComponent(container);
  }
});