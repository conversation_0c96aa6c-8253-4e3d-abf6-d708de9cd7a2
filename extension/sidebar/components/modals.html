<!DOCTYPE html>
<!-- extension/sidebar/components/modals.html -->
<div id="modals-container">
  <!-- Create/Edit Project Modal -->
  <div id="project-modal" class="modal-overlay" style="display: none;">
    <div class="modal-container">
      <div class="modal-header">
        <h3 id="project-modal-title">Create New Project</h3>
        <button class="modal-close" id="project-modal-close">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <form id="project-form" class="modal-form">
          <div class="form-group">
            <label for="project-name">Project Name *</label>
            <input type="text" id="project-name" class="cc-input" placeholder="Enter project name" required>
          </div>

          <div class="form-group">
            <label for="project-description">Description</label>
            <textarea id="project-description" class="cc-input" rows="4" placeholder="Describe your project..."></textarea>
          </div>

          <div class="form-group">
            <label for="project-status">Status</label>
            <select id="project-status" class="cc-input">
              <option value="active">Active</option>
              <option value="draft">Draft</option>
              <option value="completed">Completed</option>
              <option value="archived">Archived</option>
            </select>
          </div>

          <div class="form-group">
            <label for="project-priority">Priority</label>
            <select id="project-priority" class="cc-input">
              <option value="medium">Medium</option>
              <option value="low">Low</option>
              <option value="high">High</option>
              <option value="urgent">Urgent</option>
            </select>
          </div>

          <div class="form-group">
            <label for="project-tags">Tags</label>
            <input type="text" id="project-tags" class="cc-input" placeholder="Enter tags separated by commas">
            <div class="tags-preview" id="project-tags-preview"></div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="cc-button secondary" id="project-modal-cancel">Cancel</button>
        <button type="button" class="cc-button primary" id="project-modal-save">Create Project</button>
      </div>
    </div>
  </div>

  <!-- Create/Edit Persona Modal -->
  <div id="persona-modal" class="modal-overlay" style="display: none;">
    <div class="modal-container">
      <div class="modal-header">
        <h3 id="persona-modal-title">Create New Persona</h3>
        <button class="modal-close" id="persona-modal-close">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <form id="persona-form" class="modal-form">
          <div class="form-row">
            <div class="form-group">
              <label for="persona-name">Name *</label>
              <input type="text" id="persona-name" class="cc-input" placeholder="Persona name" required>
            </div>
            <div class="form-group">
              <label for="persona-role">Role</label>
              <select id="persona-role" class="cc-input">
                <option value="assistant">Assistant</option>
                <option value="expert">Expert</option>
                <option value="reviewer">Reviewer</option>
                <option value="mentor">Mentor</option>
                <option value="analyst">Analyst</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label for="persona-description">Description</label>
            <textarea id="persona-description" class="cc-input" rows="3" placeholder="Describe this persona..."></textarea>
          </div>

          <div class="form-group">
            <label for="persona-system-prompt">System Prompt</label>
            <textarea id="persona-system-prompt" class="cc-input" rows="6" placeholder="You are [persona name], [description]. You specialize in [expertise]..."></textarea>
            <div class="form-help">Define how this persona should behave and respond</div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="persona-temperature">Temperature</label>
              <input type="range" id="persona-temperature" class="cc-input" min="0" max="2" step="0.1" value="0.7">
              <span class="range-value" id="persona-temp-value">0.7</span>
            </div>
            <div class="form-group">
              <label for="persona-max-tokens">Max Tokens</label>
              <input type="number" id="persona-max-tokens" class="cc-input" min="100" max="4000" value="1000">
            </div>
          </div>

          <div class="form-group">
            <label for="persona-expertise">Expertise Areas</label>
            <input type="text" id="persona-expertise" class="cc-input" placeholder="JavaScript, React, Node.js">
            <div class="tags-preview" id="persona-expertise-preview"></div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="persona-personality">Personality</label>
              <select id="persona-personality" class="cc-input">
                <option value="professional">Professional</option>
                <option value="friendly">Friendly</option>
                <option value="technical">Technical</option>
                <option value="creative">Creative</option>
                <option value="concise">Concise</option>
              </select>
            </div>
            <div class="form-group">
              <label for="persona-color">Theme Color</label>
              <input type="color" id="persona-color" class="cc-input" value="#3b82f6">
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="cc-button secondary" id="persona-modal-cancel">Cancel</button>
        <button type="button" class="cc-button primary" id="persona-modal-save">Create Persona</button>
      </div>
    </div>
  </div>

  <!-- Create/Edit Artifact Modal -->
  <div id="artifact-modal" class="modal-overlay" style="display: none;">
    <div class="modal-container">
      <div class="modal-header">
        <h3 id="artifact-modal-title">Create New Artifact</h3>
        <button class="modal-close" id="artifact-modal-close">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <form id="artifact-form" class="modal-form">
          <div class="form-row">
            <div class="form-group">
              <label for="artifact-name">Artifact Name *</label>
              <input type="text" id="artifact-name" class="cc-input" placeholder="Enter artifact name" required>
            </div>
            <div class="form-group">
              <label for="artifact-project">Project</label>
              <select id="artifact-project" class="cc-input">
                <option value="">Select Project</option>
                <!-- Projects will be populated dynamically -->
              </select>
            </div>
          </div>

          <div class="form-group">
            <label for="artifact-description">Description</label>
            <textarea id="artifact-description" class="cc-input" rows="3" placeholder="Brief description of this artifact..."></textarea>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="artifact-status">Status</label>
              <select id="artifact-status" class="cc-input">
                <option value="draft">Draft</option>
                <option value="in-progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="archived">Archived</option>
              </select>
            </div>
            <div class="form-group">
              <label for="artifact-priority">Priority</label>
              <select id="artifact-priority" class="cc-input">
                <option value="medium">Medium</option>
                <option value="low">Low</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>
          </div>

          <div class="form-group">
            <label for="artifact-content">Content</label>
            <textarea id="artifact-content" class="cc-input" rows="8" placeholder="Start writing your artifact content here..."></textarea>
            <div class="form-help">You can continue editing this artifact in the full editor after creation</div>
          </div>

          <div class="form-group">
            <label for="artifact-tags">Tags</label>
            <input type="text" id="artifact-tags" class="cc-input" placeholder="Enter tags separated by commas">
            <div class="tags-preview" id="artifact-tags-preview"></div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="cc-button secondary" id="artifact-modal-cancel">Cancel</button>
        <button type="button" class="cc-button primary" id="artifact-modal-save">Create Artifact</button>
      </div>
    </div>
  </div>

  <!-- Export Configuration Modal -->
  <div id="export-modal" class="modal-overlay" style="display: none;">
    <div class="modal-container">
      <div class="modal-header">
        <h3 id="export-modal-title">Export Configuration</h3>
        <button class="modal-close" id="export-modal-close">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <form id="export-form" class="modal-form">
          <div class="form-group">
            <label for="export-type">Export Type</label>
            <select id="export-type" class="cc-input">
              <option value="single-artifact">Single Artifact</option>
              <option value="project">Project</option>
              <option value="all-projects">All Projects</option>
              <option value="project-zip">Project (ZIP)</option>
              <option value="all-projects-zip">All Projects (ZIP)</option>
            </select>
          </div>

          <div class="form-group" id="export-selection-group">
            <label for="export-selection">Select Item</label>
            <select id="export-selection" class="cc-input">
              <option value="">Choose item to export...</option>
            </select>
          </div>

          <div class="form-group">
            <label for="export-template">Export Template</label>
            <select id="export-template" class="cc-input">
              <option value="simple-markdown">Simple Markdown</option>
              <option value="with-metadata">With Metadata</option>
              <option value="complete-export">Complete Export</option>
              <option value="backup-export">Backup Export</option>
            </select>
            <div class="form-help">Choose the format and level of detail for your export</div>
          </div>

          <div class="form-options">
            <div class="form-group checkbox">
              <label>
                <input type="checkbox" id="export-include-date" checked>
                Include date in filename
              </label>
            </div>

            <div class="form-group checkbox">
              <label>
                <input type="checkbox" id="export-include-metadata" checked>
                Include metadata/front-matter
              </label>
            </div>

            <div class="form-group checkbox">
              <label>
                <input type="checkbox" id="export-compress" checked>
                Compress output (ZIP)
              </label>
            </div>
          </div>

          <div class="export-preview" id="export-preview">
            <h4>Export Preview</h4>
            <div class="preview-info">
              <div class="preview-item">
                <span class="preview-label">Files:</span>
                <span class="preview-value" id="preview-file-count">0</span>
              </div>
              <div class="preview-item">
                <span class="preview-label">Size:</span>
                <span class="preview-value" id="preview-size">0 KB</span>
              </div>
              <div class="preview-item">
                <span class="preview-label">Format:</span>
                <span class="preview-value" id="preview-format">Markdown</span>
              </div>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="cc-button secondary" id="export-modal-cancel">Cancel</button>
        <button type="button" class="cc-button primary" id="export-modal-preview">Preview Export</button>
        <button type="button" class="cc-button primary" id="export-modal-export">Export</button>
      </div>
    </div>
  </div>
</div>