<!DOCTYPE html>
<!-- extension/sidebar/components/version-control.html -->
<div id="version-control-component" class="version-control-component">
  <!-- Version Control Header -->
  <div class="version-header">
    <div class="version-info">
      <h3>Version History</h3>
      <div class="version-stats">
        <span id="version-count">0 versions</span>
      </div>
    </div>
    <div class="version-actions">
      <button id="refresh-versions-btn" class="cc-button secondary">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="23,4 23,10 17,10"/>
          <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
        </svg>
        Refresh
      </button>
      <button id="close-versions-btn" class="cc-button secondary">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"/>
          <line x1="6" y1="6" x2="18" y2="18"/>
        </svg>
        Close
      </button>
    </div>
  </div>

  <!-- Version List -->
  <div class="version-list-container">
    <div id="version-list" class="version-list">
      <div class="version-loading">
        <div class="spinner"></div>
        Loading versions...
      </div>
    </div>
  </div>

  <!-- Version Comparison -->
  <div class="version-comparison" id="version-comparison" style="display: none;">
    <div class="comparison-header">
      <div class="comparison-info">
        <span id="comparison-title">Comparing versions</span>
      </div>
      <div class="comparison-actions">
        <button id="restore-version-btn" class="cc-button">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 7v6h6"/>
            <path d="M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13"/>
          </svg>
          Restore Version
        </button>
        <button id="close-comparison-btn" class="cc-button secondary">Back to List</button>
      </div>
    </div>

    <div class="diff-container">
      <div class="diff-view">
        <div class="diff-header">
          <span class="diff-version" id="diff-version1">Version X</span>
          <span class="diff-arrow">→</span>
          <span class="diff-version" id="diff-version2">Version Y</span>
        </div>
        <div class="diff-content" id="diff-content">
          <!-- Diff content will be populated here -->
        </div>
      </div>
    </div>
  </div>
</div>