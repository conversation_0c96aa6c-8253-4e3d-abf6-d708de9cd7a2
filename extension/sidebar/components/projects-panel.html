<!DOCTYPE html>
<!-- extension/sidebar/components/projects-panel.html -->
<div id="projects-panel" class="panel projects-panel">
  <div class="panel-header">
    <div class="panel-actions">
      <button id="new-project-btn" class="cc-button primary">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="12" y1="5" x2="12" y2="19"/>
          <line x1="5" y1="12" x2="19" y2="12"/>
        </svg>
        New Project
      </button>
      <button id="refresh-projects-btn" class="cc-button secondary">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="23,4 23,10 17,10"/>
          <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
        </svg>
      </button>
    </div>
    <div class="search-container">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="11" cy="11" r="8"/>
        <path d="M21 21l-4.35-4.35"/>
      </svg>
      <input type="text" id="project-search" class="cc-input" placeholder="Search projects...">
    </div>
  </div>

  <div class="panel-content">
    <div id="projects-list" class="cc-list">
      <div class="cc-loading">
        <div class="spinner"></div>
        Loading projects...
      </div>
    </div>
  </div>

  <!-- Project Statistics -->
  <div class="panel-footer">
    <div class="stats-bar">
      <div class="stat-item">
        <span class="stat-label">Total:</span>
        <span class="stat-value" id="projects-total">0</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Active:</span>
        <span class="stat-value" id="projects-active">0</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Completed:</span>
        <span class="stat-value" id="projects-completed">0</span>
      </div>
    </div>
  </div>
</div>