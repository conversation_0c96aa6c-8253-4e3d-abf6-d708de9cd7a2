// extension/sidebar/components/version-control.js - Version control functionality

class VersionControlComponent {
  constructor(container) {
    this.container = container;
    this.currentArtifactId = null;
    this.versions = [];
    this.selectedVersions = [];
    this.comparisonMode = false;

    this.init();
  }

  async init() {
    console.log('[VersionControl] Initializing component...');

    this.setupEventListeners();

    // Load modules
    await this.loadModules();

    // Load initial data
    await this.loadVersionHistory();

    console.log('[VersionControl] Component initialized');
  }

  async loadModules() {
    // In a real implementation, these would be imported properly
    this.artifactEditor = window.artifactEditor;
    this.projectManager = window.projectManager;
    this.personaWorkflow = window.personaWorkflow;
  }

  setupEventListeners() {
    // Refresh versions button
    const refreshBtn = this.container.querySelector('#refresh-versions-btn');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => this.loadVersionHistory());
    }

    // Close versions button
    const closeBtn = this.container.querySelector('#close-versions-btn');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => this.closeVersionControl());
    }

    // Restore version button
    const restoreBtn = this.container.querySelector('#restore-version-btn');
    if (restoreBtn) {
      restoreBtn.addEventListener('click', () => this.restoreSelectedVersion());
    }

    // Close comparison button
    const closeComparisonBtn = this.container.querySelector('#close-comparison-btn');
    if (closeComparisonBtn) {
      closeComparisonBtn.addEventListener('click', () => this.closeComparison());
    }
  }

  async loadVersionHistory() {
    console.log('[VersionControl] Loading version history...');

    const versionList = this.container.querySelector('#version-list');
    if (!versionList) return;

    // Show loading state
    versionList.innerHTML = `
      <div class="version-loading">
        <div class="spinner"></div>
        Loading versions...
      </div>
    `;

    try {
      // In a real implementation, get the current artifact ID
      // For now, we'll create mock version data
      await this.loadMockVersions();

      this.renderVersionList();
      this.updateVersionCount();

    } catch (error) {
      console.error('[VersionControl] Failed to load versions:', error);
      versionList.innerHTML = `
        <div class="version-loading" style="color: #ef4444;">
          Failed to load version history
        </div>
      `;
    }
  }

  async loadMockVersions() {
    // Mock version data for demonstration
    this.versions = [
      {
        id: 'version_001',
        artifactId: 'artifact_001',
        version: 5,
        changeSummary: 'Fixed formatting and improved structure',
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        wordCount: 450,
        content: '# Improved Documentation\n\nThis is the improved version with better formatting.'
      },
      {
        id: 'version_002',
        artifactId: 'artifact_001',
        version: 4,
        changeSummary: 'Added new section about implementation',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
        wordCount: 380,
        content: '# Documentation\n\nThis version includes implementation details.'
      },
      {
        id: 'version_003',
        artifactId: 'artifact_001',
        version: 3,
        changeSummary: 'Initial content draft',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
        wordCount: 250,
        content: '# Documentation\n\nInitial draft of the documentation.'
      }
    ];

    // Simulate loading delay
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  renderVersionList() {
    const versionList = this.container.querySelector('#version-list');
    if (!versionList) return;

    if (this.versions.length === 0) {
      versionList.innerHTML = `
        <div class="empty-state">
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5.586a1 1 0 0 1 .707.293l5.414 5.414a1 1 0 0 1 .293.707V19a2 2 0 0 1-2 2z"/>
          </svg>
          <h3>No versions found</h3>
          <p>Start editing to create your first version</p>
        </div>
      `;
      return;
    }

    const versionsHtml = this.versions.map((version, index) => {
      const isCurrent = index === 0;
      const date = new Date(version.timestamp);
      const relativeTime = this.getRelativeTime(date);

      return `
        <div class="version-item ${isCurrent ? 'current' : ''}" data-version-id="${version.id}">
          <div class="version-item-info">
            <div class="version-number">Version ${version.version}</div>
            <div class="version-summary">${version.changeSummary}</div>
            <div class="version-meta">
              <span>${relativeTime}</span>
              <span>${version.wordCount} words</span>
            </div>
          </div>
          <div class="version-actions">
            <button class="version-btn" onclick="event.stopPropagation(); window.versionControlComponent.compareVersions('${version.id}')">
              Compare
            </button>
            ${!isCurrent ? `
              <button class="version-btn primary" onclick="event.stopPropagation(); window.versionControlComponent.restoreVersion('${version.id}')">
                Restore
              </button>
            ` : ''}
          </div>
        </div>
      `;
    }).join('');

    versionList.innerHTML = versionsHtml;

    // Add click handlers for version items
    versionList.querySelectorAll('.version-item').forEach(item => {
      item.addEventListener('click', () => {
        const versionId = item.dataset.versionId;
        this.selectVersion(versionId);
      });
    });
  }

  updateVersionCount() {
    const countElement = this.container.querySelector('#version-count');
    if (countElement) {
      countElement.textContent = `${this.versions.length} version${this.versions.length !== 1 ? 's' : ''}`;
    }
  }

  getRelativeTime(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days !== 1 ? 's' : ''} ago`;
    }
  }

  selectVersion(versionId) {
    console.log('[VersionControl] Selected version:', versionId);

    // Toggle selection
    const versionElement = this.container.querySelector(`[data-version-id="${versionId}"]`);
    if (!versionElement) return;

    const isSelected = versionElement.classList.contains('selected');

    // Clear previous selections
    this.container.querySelectorAll('.version-item.selected').forEach(item => {
      item.classList.remove('selected');
    });

    if (!isSelected) {
      versionElement.classList.add('selected');
      this.selectedVersions = [versionId];
    } else {
      this.selectedVersions = [];
    }
  }

  async compareVersions(versionId) {
    console.log('[VersionControl] Comparing version:', versionId);

    if (this.versions.length < 2) {
      this.showNotification('Need at least 2 versions to compare', 'warning');
      return;
    }

    const currentVersion = this.versions[0];
    const compareVersion = this.versions.find(v => v.id === versionId);

    if (!compareVersion) {
      this.showNotification('Version not found', 'error');
      return;
    }

    // Generate diff
    const diff = this.generateDiff(compareVersion.content, currentVersion.content);

    // Show comparison view
    this.showComparison(compareVersion, currentVersion, diff);
  }

  async restoreVersion(versionId) {
    console.log('[VersionControl] Restoring version:', versionId);

    const version = this.versions.find(v => v.id === versionId);
    if (!version) {
      this.showNotification('Version not found', 'error');
      return;
    }

    try {
      // In a real implementation, this would call the artifact editor
      console.log('[VersionControl] Version restored:', versionId);
      this.showNotification('Version restored successfully!', 'success');

      // Reload version history
      await this.loadVersionHistory();

    } catch (error) {
      console.error('[VersionControl] Failed to restore version:', error);
      this.showNotification('Failed to restore version', 'error');
    }
  }

  generateDiff(oldContent, newContent) {
    // Simple line-by-line diff implementation
    const oldLines = oldContent.split('\n');
    const newLines = newContent.split('\n');
    const diff = [];

    const maxLines = Math.max(oldLines.length, newLines.length);

    for (let i = 0; i < maxLines; i++) {
      const oldLine = oldLines[i] || '';
      const newLine = newLines[i] || '';

      if (oldLine !== newLine) {
        if (oldLine && !newLine) {
          diff.push({
            type: 'removed',
            line: i + 1,
            old: oldLine,
            new: ''
          });
        } else if (!oldLine && newLine) {
          diff.push({
            type: 'added',
            line: i + 1,
            old: '',
            new: newLine
          });
        } else {
          diff.push({
            type: 'modified',
            line: i + 1,
            old: oldLine,
            new: newLine
          });
        }
      } else if (oldLine) {
        diff.push({
          type: 'unchanged',
          line: i + 1,
          old: oldLine,
          new: newLine
        });
      }
    }

    return diff;
  }

  showComparison(version1, version2, diff) {
    console.log('[VersionControl] Showing comparison');

    // Hide version list and show comparison
    const versionList = this.container.querySelector('#version-list');
    const comparison = this.container.querySelector('#version-comparison');

    if (versionList && comparison) {
      versionList.style.display = 'none';
      comparison.style.display = 'flex';
    }

    // Update comparison info
    const comparisonTitle = this.container.querySelector('#comparison-title');
    const version1Element = this.container.querySelector('#diff-version1');
    const version2Element = this.container.querySelector('#diff-version2');

    if (comparisonTitle) {
      comparisonTitle.textContent = `Comparing Version ${version1.version} ↔ Version ${version2.version}`;
    }

    if (version1Element) {
      version1Element.textContent = `Version ${version1.version}`;
    }

    if (version2Element) {
      version2Element.textContent = `Version ${version2.version}`;
    }

    // Render diff
    this.renderDiff(diff);

    this.comparisonMode = true;
  }

  renderDiff(diff) {
    const diffContent = this.container.querySelector('#diff-content');
    if (!diffContent) return;

    const diffHtml = diff.map(change => {
      const className = `diff-${change.type}`;
      let content = '';

      switch (change.type) {
        case 'added':
          content = `+ ${change.new}`;
          break;
        case 'removed':
          content = `- ${change.old}`;
          break;
        case 'modified':
          content = `~ Line ${change.line}: ${change.old} → ${change.new}`;
          break;
        case 'unchanged':
          content = `  ${change.old}`;
          break;
      }

      return `<div class="${className}">${content}</div>`;
    }).join('');

    diffContent.innerHTML = diffHtml;
  }

  closeComparison() {
    console.log('[VersionControl] Closing comparison');

    // Show version list and hide comparison
    const versionList = this.container.querySelector('#version-list');
    const comparison = this.container.querySelector('#version-comparison');

    if (versionList && comparison) {
      versionList.style.display = 'flex';
      comparison.style.display = 'none';
    }

    this.comparisonMode = false;
  }

  restoreSelectedVersion() {
    if (this.selectedVersions.length === 0) {
      this.showNotification('No version selected', 'warning');
      return;
    }

    this.restoreVersion(this.selectedVersions[0]);
  }

  closeVersionControl() {
    console.log('[VersionControl] Closing version control');

    // In a real implementation, this would hide the component
    // For now, just show a notification
    this.showNotification('Version control closed');
  }

  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : type === 'warning' ? '#f59e0b' : '#3b82f6'};
      color: white;
      padding: 12px 16px;
      border-radius: 6px;
      z-index: 10000;
      font-size: 14px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      animation: slideInRight 0.3s ease;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.animation = 'slideOutRight 0.3s ease';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }
}

// CSS for animations
const style = document.createElement('style');
style.textContent = `
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
`;
document.head.appendChild(style);

// Initialize when component is loaded
document.addEventListener('DOMContentLoaded', () => {
  const container = document.getElementById('version-control-component');
  if (container) {
    window.versionControlComponent = new VersionControlComponent(container);
  }
});