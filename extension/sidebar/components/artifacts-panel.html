<!DOCTYPE html>
<!-- extension/sidebar/components/artifacts-panel.html -->
<div id="artifacts-panel" class="panel artifacts-panel">
  <div class="panel-header">
    <div class="panel-actions">
      <button id="new-artifact-btn" class="cc-button primary">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="12" y1="5" x2="12" y2="19"/>
          <line x1="5" y1="12" x2="19" y2="12"/>
        </svg>
        New Artifact
      </button>
      <button id="refresh-artifacts-btn" class="cc-button secondary">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="23,4 23,10 17,10"/>
          <path d="M20.49,15A9,9,0,1,1,5.64,5.64L23,10"/>
        </svg>
      </button>
    </div>
    <div class="search-container">
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="11" cy="11" r="8"/>
        <path d="M21 21l-4.35-4.35"/>
      </svg>
      <input type="text" id="artifact-search" class="cc-input" placeholder="Search artifacts...">
    </div>
  </div>

  <div class="panel-content">
    <!-- Current Project Filter -->
    <div class="project-filter-section">
      <div class="section-header">
        <h4>Filter by Project</h4>
      </div>
      <div class="project-filter">
        <select id="project-filter" class="cc-input">
          <option value="">All Projects</option>
          <!-- Projects will be populated dynamically -->
        </select>
      </div>
    </div>

    <!-- Artifacts List -->
    <div class="section-header">
      <h4>Artifacts</h4>
    </div>
    <div id="artifacts-list" class="cc-list">
      <div class="cc-loading">
        <div class="spinner"></div>
        Loading artifacts...
      </div>
    </div>
  </div>

  <!-- Artifact Statistics -->
  <div class="panel-footer">
    <div class="stats-bar">
      <div class="stat-item">
        <span class="stat-label">Total:</span>
        <span class="stat-value" id="artifacts-total">0</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Words:</span>
        <span class="stat-value" id="artifacts-words">0</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Versions:</span>
        <span class="stat-value" id="artifacts-versions">0</span>
      </div>
    </div>
  </div>
</div>