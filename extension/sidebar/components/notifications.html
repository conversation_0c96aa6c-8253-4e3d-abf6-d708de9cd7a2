<!DOCTYPE html>
<!-- extension/sidebar/components/notifications.html -->
<div id="notifications-container" class="notifications-container">
  <!-- Toast Notifications -->
  <div id="toast-container" class="toast-container">
    <!-- Toasts will be added here dynamically -->
  </div>

  <!-- Progress Indicators -->
  <div id="progress-container" class="progress-container" style="display: none;">
    <div class="progress-overlay">
      <div class="progress-content">
        <div class="progress-spinner"></div>
        <div class="progress-text" id="progress-text">Processing...</div>
        <div class="progress-bar">
          <div class="progress-fill" id="progress-fill" style="width: 0%;"></div>
        </div>
        <button class="progress-cancel" id="progress-cancel" style="display: none;">Cancel</button>
      </div>
    </div>
  </div>

  <!-- Undo/Redo Bar -->
  <div id="undo-redo-bar" class="undo-redo-bar" style="display: none;">
    <div class="undo-redo-content">
      <button class="undo-btn" id="undo-btn" disabled>
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="9,14 4,9 9,4"/>
          <path d="M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5v1.5"/>
        </svg>
        Undo
      </button>
      <span class="undo-redo-message" id="undo-redo-message">Action completed</span>
      <button class="redo-btn" id="redo-btn" disabled>
        Redo
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="15,14 20,9 15,4"/>
          <path d="M20 9H9.5A5.5 5.5 0 0 0 4 14.5V16"/>
        </svg>
      </button>
    </div>
  </div>
</div>