<!DOCTYPE html>
<!-- extension/sidebar/components/artifact-editor.html -->
<div id="artifact-editor-component" class="artifact-editor-component">
  <!-- Editor Header -->
  <div class="editor-header">
    <div class="editor-info">
      <h3 id="editor-title" class="editor-title">Untitled Artifact</h3>
      <div class="editor-meta">
        <span id="editor-status" class="status-indicator draft">Draft</span>
        <span id="editor-word-count">0 words</span>
        <span id="editor-last-saved">Not saved</span>
      </div>
    </div>
    <div class="editor-actions">
      <button id="editor-save-btn" class="cc-button">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
          <polyline points="17,8 17,3 12,3"/>
        </svg>
        Save
      </button>
      <button id="editor-preview-btn" class="cc-button secondary">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
          <circle cx="12" cy="12" r="3"/>
        </svg>
        Preview
      </button>
      <button id="editor-settings-btn" class="cc-button secondary">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="3"/>
          <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"/>
        </svg>
      </button>
    </div>
  </div>

  <!-- Editor Toolbar -->
  <div class="editor-toolbar">
    <div class="toolbar-group">
      <button class="toolbar-btn" data-command="bold" title="Bold (Ctrl+B)">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"/>
          <path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"/>
        </svg>
      </button>
      <button class="toolbar-btn" data-command="italic" title="Italic (Ctrl+I)">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="19" y1="4" x2="10" y2="4"/>
          <line x1="14" y1="20" x2="5" y2="20"/>
          <line x1="15" y1="4" x2="9" y2="20"/>
        </svg>
      </button>
      <button class="toolbar-btn" data-command="code" title="Inline Code (Ctrl+`)">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="16,18 22,12 16,6"/>
          <polyline points="8,6 2,12 8,18"/>
        </svg>
      </button>
    </div>

    <div class="toolbar-group">
      <button class="toolbar-btn" data-command="h1" title="Heading 1">H1</button>
      <button class="toolbar-btn" data-command="h2" title="Heading 2">H2</button>
      <button class="toolbar-btn" data-command="h3" title="Heading 3">H3</button>
    </div>

    <div class="toolbar-group">
      <button class="toolbar-btn" data-command="ul" title="Bullet List">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="8" y1="6" x2="21" y2="6"/>
          <line x1="8" y1="12" x2="21" y2="12"/>
          <line x1="8" y1="18" x2="21" y2="18"/>
          <line x1="3" y1="6" x2="3.01" y2="6"/>
          <line x1="3" y1="12" x2="3.01" y2="12"/>
          <line x1="3" y1="18" x2="3.01" y2="18"/>
        </svg>
      </button>
      <button class="toolbar-btn" data-command="ol" title="Numbered List">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="10" y1="6" x2="21" y2="6"/>
          <line x1="10" y1="12" x2="21" y2="12"/>
          <line x1="10" y1="18" x2="21" y2="18"/>
          <path d="M4 6h1v4"/>
          <path d="M4 10h2"/>
          <path d="M6 18H4c0-1 2-2 2-3s-1-1.5-2-1"/>
        </svg>
      </button>
      <button class="toolbar-btn" data-command="quote" title="Quote">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 2 1 2 2s-1 2-2 2H3z"/>
          <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4H15z"/>
        </svg>
      </button>
    </div>

    <div class="toolbar-group">
      <button class="toolbar-btn" data-command="link" title="Insert Link">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
          <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
        </svg>
      </button>
      <button class="toolbar-btn" data-command="image" title="Insert Image">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <circle cx="9" cy="9" r="2"/>
          <path d="M21 15l-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
        </svg>
      </button>
      <button class="toolbar-btn" data-command="codeblock" title="Code Block">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M16 18l6-6-6-6"/>
          <path d="M8 6l-6 6 6 6"/>
        </svg>
      </button>
    </div>

    <div class="toolbar-group">
      <button class="toolbar-btn" data-command="undo" title="Undo (Ctrl+Z)">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="9,14 4,9 9,4"/>
          <path d="M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5v1.5"/>
        </svg>
      </button>
      <button class="toolbar-btn" data-command="redo" title="Redo (Ctrl+Y)">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="15,14 20,9 15,4"/>
          <path d="M20 9H9.5A5.5 5.5 0 0 0 4 14.5V16"/>
        </svg>
      </button>
    </div>
  </div>

  <!-- Editor Container -->
  <div class="editor-container">
    <!-- Editor Pane -->
    <div class="editor-pane">
      <div class="editor-wrapper">
        <textarea id="markdown-editor" class="markdown-editor" placeholder="Start writing your artifact..."></textarea>
      </div>
    </div>

    <!-- Preview Pane -->
    <div class="preview-pane" id="preview-pane">
      <div class="preview-wrapper">
        <div id="markdown-preview" class="markdown-preview"></div>
      </div>
    </div>

    <!-- Resizer -->
    <div class="editor-resizer" id="editor-resizer">
      <div class="resizer-handle"></div>
    </div>
  </div>

  <!-- Editor Footer -->
  <div class="editor-footer">
    <div class="editor-mode-toggle">
      <button id="edit-mode-btn" class="mode-btn active">Edit</button>
      <button id="preview-mode-btn" class="mode-btn">Preview</button>
      <button id="split-mode-btn" class="mode-btn active">Split</button>
    </div>
    <div class="editor-search">
      <input type="text" id="editor-search-input" placeholder="Search...">
      <button id="editor-search-prev" class="search-btn">↑</button>
      <button id="editor-search-next" class="search-btn">↓</button>
      <button id="editor-search-close" class="search-btn">×</button>
    </div>
  </div>
</div>