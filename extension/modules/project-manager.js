// extension/modules/project-manager.js - Project management and CRUD operations

class ProjectManager {
  constructor() {
    this.storage = null;
    this.slugGenerator = null;
    this.PROJECTS_KEY = 'projects';
    this.init();
  }

  async init() {
    console.log('[ProjectManager] Initializing...');

    // Dynamically import utility modules
    if (typeof importScripts !== 'undefined') {
      // For content script environment
      this.storage = window.storageManager;
      this.slugGenerator = window.slugGenerator;
    } else {
      // For other environments, create instances
      this.storage = await this.loadStorageModule();
      this.slugGenerator = await this.loadSlugGeneratorModule();
    }
  }

  async loadStorageModule() {
    // This will be available after proper module system is set up
    // For now, return a placeholder that will be replaced with actual storage
    return {
      get: async (key, defaultValue = null) => {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : defaultValue;
      },
      set: async (key, value) => {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
      },
      remove: async (key) => {
        localStorage.removeItem(key);
        return true;
      }
    };
  }

  async loadSlugGeneratorModule() {
    return {
      generate: (text, options = {}) => {
        return text
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/^-+|-+$/g, '');
      }
    };
  }

  // Core CRUD Operations

  /**
   * Create a new project
   * @param {string} name - Project name
   * @param {string} description - Project description
   * @param {Array} personas - Array of persona IDs
   * @param {Object} options - Additional options
   * @returns {Object} Created project object
   */
  async createProject(name, description = '', personas = [], options = {}) {
    console.log('[ProjectManager] Creating project:', name);

    try {
      const projectData = {
        id: this.generateProjectId(),
        name: name.trim(),
        description: description.trim(),
        slug: this.generateProjectSlug(name),
        personas: [...personas],
        artifacts: [],
        status: options.status || 'active',
        priority: options.priority || 'medium',
        tags: options.tags || [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastModified: new Date().toISOString(),
        version: 1,
        metadata: options.metadata || {}
      };

      // Validate project data
      this.validateProjectData(projectData);

      // Get existing projects
      const projects = await this.getAllProjects(true);

      // Check for duplicate slugs
      const existingSlugs = projects.map(p => p.slug);
      if (existingSlugs.includes(projectData.slug)) {
        projectData.slug = `${projectData.slug}-${Date.now()}`;
      }

      // Add to projects array
      projects.push(projectData);

      // Save updated projects
      await this.storage.set(this.PROJECTS_KEY, projects);

      console.log('[ProjectManager] Project created successfully:', projectData.id);
      return projectData;

    } catch (error) {
      console.error('[ProjectManager] Failed to create project:', error);
      throw new Error(`Failed to create project: ${error.message}`);
    }
  }

  /**
   * Get a project by ID
   * @param {string} projectId - Project ID
   * @returns {Object|null} Project object or null if not found
   */
  async getProject(projectId) {
    try {
      const projects = await this.getAllProjects(true);
      return projects.find(p => p.id === projectId) || null;
    } catch (error) {
      console.error('[ProjectManager] Failed to get project:', error);
      return null;
    }
  }

  /**
   * Get all projects
   * @param {boolean} includeArchived - Whether to include archived projects
   * @returns {Array} Array of project objects
   */
  async getAllProjects(includeArchived = false) {
    try {
      const projects = await this.storage.get(this.PROJECTS_KEY, []);

      if (!Array.isArray(projects)) {
        console.warn('[ProjectManager] Projects data is not an array, resetting');
        return [];
      }

      if (includeArchived) {
        return projects;
      }

      return projects.filter(p => p.status !== 'archived');
    } catch (error) {
      console.error('[ProjectManager] Failed to get projects:', error);
      return [];
    }
  }

  /**
   * Update project metadata
   * @param {string} projectId - Project ID
   * @param {Object} updates - Updates to apply
   * @returns {Object|null} Updated project or null if not found
   */
  async updateProjectMetadata(projectId, updates) {
    console.log('[ProjectManager] Updating project:', projectId);

    try {
      const projects = await this.getAllProjects(true);
      const projectIndex = projects.findIndex(p => p.id === projectId);

      if (projectIndex === -1) {
        throw new Error('Project not found');
      }

      // Apply updates
      const updatedProject = {
        ...projects[projectIndex],
        ...updates,
        updatedAt: new Date().toISOString(),
        lastModified: new Date().toISOString(),
        version: (projects[projectIndex].version || 1) + 1
      };

      // Validate updated project
      this.validateProjectData(updatedProject);

      // Update projects array
      projects[projectIndex] = updatedProject;
      await this.storage.set(this.PROJECTS_KEY, projects);

      console.log('[ProjectManager] Project updated successfully:', projectId);
      return updatedProject;

    } catch (error) {
      console.error('[ProjectManager] Failed to update project:', error);
      throw error;
    }
  }

  /**
   * Update a project (alias for updateProjectMetadata)
   * @param {string} projectId - Project ID
   * @param {Object} updates - Updates to apply
   * @returns {Object|null} Updated project or null if not found
   */
  async updateProject(projectId, updates) {
    return this.updateProjectMetadata(projectId, updates);
  }

  /**
   * Archive a project
   * @param {string} projectId - Project ID
   * @returns {boolean} Success status
   */
  async archiveProject(projectId) {
    console.log('[ProjectManager] Archiving project:', projectId);

    try {
      const result = await this.updateProjectMetadata(projectId, {
        status: 'archived',
        archivedAt: new Date().toISOString()
      });

      console.log('[ProjectManager] Project archived successfully:', projectId);
      return !!result;

    } catch (error) {
      console.error('[ProjectManager] Failed to archive project:', error);
      return false;
    }
  }

  /**
   * Delete a project
   * @param {string} projectId - Project ID
   * @returns {boolean} Success status
   */
  async deleteProject(projectId) {
    console.log('[ProjectManager] Deleting project:', projectId);

    try {
      const projects = await this.getAllProjects(true);
      const filteredProjects = projects.filter(p => p.id !== projectId);

      if (filteredProjects.length === projects.length) {
        throw new Error('Project not found');
      }

      await this.storage.set(this.PROJECTS_KEY, filteredProjects);

      console.log('[ProjectManager] Project deleted successfully:', projectId);
      return true;

    } catch (error) {
      console.error('[ProjectManager] Failed to delete project:', error);
      return false;
    }
  }

  /**
   * Duplicate a project
   * @param {string} projectId - Project ID to duplicate
   * @param {Object} options - Duplication options
   * @returns {Object|null} Duplicated project or null if source not found
   */
  async duplicateProject(projectId, options = {}) {
    console.log('[ProjectManager] Duplicating project:', projectId);

    try {
      const sourceProject = await this.getProject(projectId);
      if (!sourceProject) {
        throw new Error('Source project not found');
      }

      const duplicateName = options.name || `${sourceProject.name} (Copy)`;
      const duplicateDescription = options.description || sourceProject.description;

      // Create new project with copied data
      const duplicatedProject = await this.createProject(
        duplicateName,
        duplicateDescription,
        [...sourceProject.personas],
        {
          status: 'draft',
          priority: sourceProject.priority,
          tags: [...sourceProject.tags],
          metadata: { ...sourceProject.metadata, duplicatedFrom: projectId }
        }
      );

      console.log('[ProjectManager] Project duplicated successfully:', duplicatedProject.id);
      return duplicatedProject;

    } catch (error) {
      console.error('[ProjectManager] Failed to duplicate project:', error);
      throw error;
    }
  }

  // Data Retrieval Methods

  /**
   * Search projects
   * @param {string} query - Search query
   * @param {Object} filters - Additional filters
   * @returns {Array} Array of matching projects
   */
  async searchProjects(query, filters = {}) {
    try {
      let projects = await this.getAllProjects(filters.includeArchived);

      // Apply search query
      if (query && query.trim()) {
        const searchTerm = query.toLowerCase().trim();
        projects = projects.filter(project =>
          project.name.toLowerCase().includes(searchTerm) ||
          project.description.toLowerCase().includes(searchTerm) ||
          project.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
      }

      // Apply filters
      if (filters.status) {
        projects = projects.filter(p => p.status === filters.status);
      }

      if (filters.priority) {
        projects = projects.filter(p => p.priority === filters.priority);
      }

      if (filters.tags && filters.tags.length > 0) {
        projects = projects.filter(p =>
          filters.tags.some(tag => p.tags.includes(tag))
        );
      }

      if (filters.personaId) {
        projects = projects.filter(p => p.personas.includes(filters.personaId));
      }

      return projects;

    } catch (error) {
      console.error('[ProjectManager] Failed to search projects:', error);
      return [];
    }
  }

  /**
   * Get projects by status
   * @param {string} status - Project status
   * @returns {Array} Array of projects with the specified status
   */
  async getProjectsByStatus(status) {
    return this.searchProjects('', { status });
  }

  /**
   * Get projects by persona
   * @param {string} personaId - Persona ID
   * @returns {Array} Array of projects using the persona
   */
  async getProjectsByPersona(personaId) {
    return this.searchProjects('', { personaId });
  }

  // Utility Methods

  /**
   * Generate a unique project ID
   * @returns {string} Unique project ID
   */
  generateProjectId() {
    return `project_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate a project slug
   * @param {string} name - Project name
   * @returns {string} Generated slug
   */
  generateProjectSlug(name) {
    if (this.slugGenerator && typeof this.slugGenerator.generate === 'function') {
      return this.slugGenerator.generate(name);
    }
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  /**
   * Validate project data
   * @param {Object} projectData - Project data to validate
   * @throws {Error} If validation fails
   */
  validateProjectData(projectData) {
    if (!projectData.name || typeof projectData.name !== 'string') {
      throw new Error('Project name is required and must be a string');
    }

    if (projectData.name.trim().length === 0) {
      throw new Error('Project name cannot be empty');
    }

    if (projectData.name.length > 100) {
      throw new Error('Project name cannot exceed 100 characters');
    }

    if (projectData.description && projectData.description.length > 1000) {
      throw new Error('Project description cannot exceed 1000 characters');
    }

    const validStatuses = ['active', 'draft', 'archived', 'completed'];
    if (projectData.status && !validStatuses.includes(projectData.status)) {
      throw new Error(`Invalid project status: ${projectData.status}`);
    }

    const validPriorities = ['low', 'medium', 'high', 'urgent'];
    if (projectData.priority && !validPriorities.includes(projectData.priority)) {
      throw new Error(`Invalid project priority: ${projectData.priority}`);
    }

    if (projectData.personas && !Array.isArray(projectData.personas)) {
      throw new Error('Personas must be an array');
    }

    if (projectData.tags && !Array.isArray(projectData.tags)) {
      throw new Error('Tags must be an array');
    }
  }

  /**
   * Get project statistics
   * @returns {Object} Project statistics
   */
  async getProjectStats() {
    try {
      const projects = await this.getAllProjects(true);

      const stats = {
        total: projects.length,
        active: projects.filter(p => p.status === 'active').length,
        draft: projects.filter(p => p.status === 'draft').length,
        archived: projects.filter(p => p.status === 'archived').length,
        completed: projects.filter(p => p.status === 'completed').length,
        byPriority: {
          low: projects.filter(p => p.priority === 'low').length,
          medium: projects.filter(p => p.priority === 'medium').length,
          high: projects.filter(p => p.priority === 'high').length,
          urgent: projects.filter(p => p.priority === 'urgent').length
        }
      };

      return stats;

    } catch (error) {
      console.error('[ProjectManager] Failed to get project stats:', error);
      return {
        total: 0,
        active: 0,
        draft: 0,
        archived: 0,
        completed: 0,
        byPriority: { low: 0, medium: 0, high: 0, urgent: 0 }
      };
    }
  }

  /**
   * Cleanup old archived projects
   * @param {number} daysOld - Remove archived projects older than this many days
   * @returns {number} Number of projects cleaned up
   */
  async cleanupArchivedProjects(daysOld = 90) {
    console.log(`[ProjectManager] Cleaning up archived projects older than ${daysOld} days`);

    try {
      const projects = await this.getAllProjects(true);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      let cleanedCount = 0;
      const activeProjects = projects.filter(project => {
        if (project.status === 'archived' && project.archivedAt) {
          const archivedDate = new Date(project.archivedAt);
          if (archivedDate < cutoffDate) {
            cleanedCount++;
            return false;
          }
        }
        return true;
      });

      if (cleanedCount > 0) {
        await this.storage.set(this.PROJECTS_KEY, activeProjects);
        console.log(`[ProjectManager] Cleaned up ${cleanedCount} archived projects`);
      }

      return cleanedCount;

    } catch (error) {
      console.error('[ProjectManager] Failed to cleanup archived projects:', error);
      return 0;
    }
  }
}

// Export for use in other modules
const projectManager = new ProjectManager();