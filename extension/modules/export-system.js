// extension/modules/export-system.js - Export system for projects, artifacts, and personas

class ExportSystem {
  constructor() {
    this.storage = null;
    this.slugGenerator = null;
    this.yamlProcessor = null;
    this.artifactEditor = null;
    this.projectManager = null;
    this.personaWorkflow = null;

    this.defaultExportOptions = {
      includeMetadata: true,
      includeVersions: false,
      fileExtension: '.md',
      dateFormat: 'YYYY-MM-DD',
      compressionLevel: 6,
      includeEmptyFolders: false
    };

    this.init();
  }

  async init() {
    console.log('[ExportSystem] Initializing...');

    // Dynamically import utility modules
    if (typeof importScripts !== 'undefined') {
      // For content script environment
      this.storage = window.storageManager;
      this.slugGenerator = window.slugGenerator;
      this.yamlProcessor = window.yamlProcessor;
      this.artifactEditor = window.artifactEditor;
      this.projectManager = window.projectManager;
      this.personaWorkflow = window.personaWorkflow;
    } else {
      // For other environments, create instances
      this.storage = await this.loadStorageModule();
      this.slugGenerator = await this.loadSlugGeneratorModule();
      this.yamlProcessor = await this.loadYAMLProcessorModule();
      this.artifactEditor = await this.loadArtifactEditorModule();
      this.projectManager = await this.loadProjectManagerModule();
      this.personaWorkflow = await this.loadPersonaWorkflowModule();
    }
  }

  async loadStorageModule() {
    return {
      get: async (key, defaultValue = null) => {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : defaultValue;
      },
      set: async (key, value) => {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
      },
      remove: async (key) => {
        localStorage.removeItem(key);
        return true;
      }
    };
  }

  async loadSlugGeneratorModule() {
    return {
      generate: (text, options = {}) => {
        return text
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/^-+|-+$/g, '');
      }
    };
  }

  async loadYAMLProcessorModule() {
    return {
      inject: (content, metadata) => {
        const yaml = Object.entries(metadata)
          .map(([key, value]) => `${key}: ${value}`)
          .join('\n');
        return `---\n${yaml}\n---\n\n${content}`;
      }
    };
  }

  async loadArtifactEditorModule() {
    return {
      getArtifact: async (id) => {
        const artifacts = await this.storage.get('artifacts', []);
        return artifacts.find(a => a.id === id);
      }
    };
  }

  async loadProjectManagerModule() {
    return {
      getProject: async (id) => {
        const projects = await this.storage.get('projects', []);
        return projects.find(p => p.id === id);
      },
      getAllProjects: async () => {
        return await this.storage.get('projects', []);
      }
    };
  }

  async loadPersonaWorkflowModule() {
    return {
      getPersona: async (id) => {
        const personas = await this.storage.get('personas', []);
        return personas.find(p => p.id === id);
      }
    };
  }

  // Single File Exports

  /**
   * Export a single artifact
   * @param {string} artifactId - Artifact ID to export
   * @param {Object} options - Export options
   * @returns {Object} Export result
   */
  async exportArtifact(artifactId, options = {}) {
    console.log('[ExportSystem] Exporting artifact:', artifactId);

    try {
      const artifact = await this.artifactEditor.getArtifact(artifactId);
      if (!artifact) {
        throw new Error('Artifact not found');
      }

      const exportOptions = { ...this.defaultExportOptions, ...options };
      const fileName = this.generateFileName(artifact.name, exportOptions.fileExtension, {
        includeDate: options.includeDate,
        dateFormat: exportOptions.dateFormat
      });

      const content = await this.prepareMarkdownContent(artifact, exportOptions.includeMetadata);

      return {
        fileName,
        content,
        contentType: 'text/markdown',
        size: content.length,
        metadata: {
          artifactId: artifact.id,
          projectId: artifact.projectId,
          personaId: artifact.personaId,
          wordCount: artifact.wordCount,
          version: artifact.version
        }
      };

    } catch (error) {
      console.error('[ExportSystem] Failed to export artifact:', error);
      throw error;
    }
  }

  /**
   * Export a single project
   * @param {string} projectId - Project ID to export
   * @param {Object} options - Export options
   * @returns {Object} Export result
   */
  async exportProject(projectId, options = {}) {
    console.log('[ExportSystem] Exporting project:', projectId);

    try {
      const project = await this.projectManager.getProject(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      const exportOptions = { ...this.defaultExportOptions, ...options };

      // Get all artifacts for the project
      const artifacts = await this.storage.get('artifacts', []);
      const projectArtifacts = artifacts.filter(a => a.projectId === projectId);

      // Generate project content
      let content = `# ${project.name}\n\n`;
      if (project.description) {
        content += `${project.description}\n\n`;
      }

      content += `## Artifacts\n\n`;
      for (const artifact of projectArtifacts) {
        content += `### ${artifact.name}\n\n`;
        content += `${artifact.content}\n\n`;
        content += `---\n\n`;
      }

      if (exportOptions.includeMetadata) {
        const metadata = {
          title: project.name,
          description: project.description,
          artifactCount: projectArtifacts.length,
          totalWords: projectArtifacts.reduce((sum, a) => sum + (a.wordCount || 0), 0),
          exportedAt: new Date().toISOString(),
          projectId: project.id
        };
        content = await this.yamlProcessor.inject(content, metadata);
      }

      const fileName = this.generateFileName(project.name, exportOptions.fileExtension, {
        includeDate: options.includeDate,
        dateFormat: exportOptions.dateFormat
      });

      return {
        fileName,
        content,
        contentType: 'text/markdown',
        size: content.length,
        metadata: {
          projectId: project.id,
          artifactCount: projectArtifacts.length,
          totalWords: projectArtifacts.reduce((sum, a) => sum + (a.wordCount || 0), 0)
        }
      };

    } catch (error) {
      console.error('[ExportSystem] Failed to export project:', error);
      throw error;
    }
  }

  // Bulk Exports

  /**
   * Export a project as ZIP archive
   * @param {string} projectId - Project ID to export
   * @param {Object} options - Export options
   * @returns {Object} ZIP export result
   */
  async exportProjectAsZip(projectId, options = {}) {
    console.log('[ExportSystem] Exporting project as ZIP:', projectId);

    try {
      const project = await this.projectManager.getProject(projectId);
      if (!project) {
        throw new Error('Project not found');
      }

      const exportOptions = { ...this.defaultExportOptions, ...options };

      // Get project artifacts
      const artifacts = await this.storage.get('artifacts', []);
      const projectArtifacts = artifacts.filter(a => a.projectId === projectId);

      // Create ZIP structure
      const zipStructure = await this.generateZipStructure([project], exportOptions);

      // Generate README for the project
      const readmeContent = await this.generateProjectReadme(project, projectArtifacts);

      const zipBlob = await this.createZipArchive(zipStructure, readmeContent);

      const fileName = this.generateFileName(project.name, '.zip', {
        includeDate: options.includeDate,
        dateFormat: exportOptions.dateFormat
      });

      return {
        fileName,
        content: zipBlob,
        contentType: 'application/zip',
        size: zipBlob.size,
        metadata: {
          projectId: project.id,
          artifactCount: projectArtifacts.length,
          isZip: true
        }
      };

    } catch (error) {
      console.error('[ExportSystem] Failed to export project as ZIP:', error);
      throw error;
    }
  }

  /**
   * Export all projects as ZIP archive
   * @param {Object} options - Export options
   * @returns {Object} ZIP export result
   */
  async exportAllProjectsAsZip(options = {}) {
    console.log('[ExportSystem] Exporting all projects as ZIP');

    try {
      const projects = await this.projectManager.getAllProjects();
      const exportOptions = { ...this.defaultExportOptions, ...options };

      // Create ZIP structure
      const zipStructure = await this.generateZipStructure(projects, exportOptions);

      // Generate main README
      const readmeContent = await this.generateMainReadme(projects);

      const zipBlob = await this.createZipArchive(zipStructure, readmeContent);

      const fileName = this.generateFileName('All-Projects', '.zip', {
        includeDate: options.includeDate,
        dateFormat: exportOptions.dateFormat
      });

      return {
        fileName,
        content: zipBlob,
        contentType: 'application/zip',
        size: zipBlob.size,
        metadata: {
          projectCount: projects.length,
          isZip: true,
          isCompleteExport: true
        }
      };

    } catch (error) {
      console.error('[ExportSystem] Failed to export all projects as ZIP:', error);
      throw error;
    }
  }

  // File Processing

  /**
   * Generate file name with proper formatting
   * @param {string} name - Base name
   * @param {string} extension - File extension
   * @param {Object} options - Naming options
   * @returns {string} Generated file name
   */
  generateFileName(name, extension, options = {}) {
    const {
      includeDate = true,
      dateFormat = 'YYYY-MM-DD',
      maxLength = 100
    } = options;

    // Generate slug from name
    let fileName = this.slugGenerator.generate(name);

    // Add date if requested
    if (includeDate) {
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD format
      fileName = `${fileName}_${dateStr}`;
    }

    // Ensure file name is not too long
    if (fileName.length > maxLength) {
      fileName = fileName.substring(0, maxLength - extension.length);
    }

    return `${fileName}${extension}`;
  }

  /**
   * Prepare markdown content with metadata
   * @param {Object} artifact - Artifact object
   * @param {boolean} includeMetadata - Whether to include YAML front-matter
   * @returns {string} Prepared content
   */
  async prepareMarkdownContent(artifact, includeMetadata = true) {
    let content = artifact.content || '';

    if (includeMetadata) {
      // Get project and persona info
      const project = await this.projectManager.getProject(artifact.projectId);
      const persona = await this.personaWorkflow.getPersona(artifact.personaId);

      // Generate metadata
      const metadata = await this.artifactEditor.generateStandardFrontMatter(
        artifact,
        project,
        persona
      );

      content = await this.yamlProcessor.inject(content, metadata);
    }

    return content;
  }

  /**
   * Generate ZIP structure for projects
   * @param {Array} projects - Projects to include
   * @param {Object} options - Export options
   * @returns {Object} ZIP structure
   */
  async generateZipStructure(projects, options) {
    const structure = {};

    for (const project of projects) {
      const projectSlug = this.slugGenerator.generate(project.name);
      const projectPath = `${projectSlug}/`;

      // Get project artifacts
      const artifacts = await this.storage.get('artifacts', []);
      const projectArtifacts = artifacts.filter(a => a.projectId === project.id);

      // Add artifacts
      for (const artifact of projectArtifacts) {
        const artifactFileName = this.generateFileName(artifact.name, '.md');
        const artifactContent = await this.prepareMarkdownContent(artifact, options.includeMetadata);

        structure[`${projectPath}${artifactFileName}`] = {
          content: artifactContent,
          metadata: {
            artifactId: artifact.id,
            projectId: project.id
          }
        };
      }

      // Add project README if multiple artifacts
      if (projectArtifacts.length > 1) {
        const readmeContent = await this.generateProjectReadme(project, projectArtifacts);
        structure[`${projectPath}README.md`] = {
          content: readmeContent,
          metadata: { type: 'readme', projectId: project.id }
        };
      }
    }

    return structure;
  }

  /**
   * Create ZIP archive from structure
   * @param {Object} structure - ZIP structure
   * @param {string} readmeContent - Main README content
   * @returns {Blob} ZIP blob
   */
  async createZipArchive(structure, readmeContent = null) {
    try {
      // Use JSZip if available
      if (typeof JSZip !== 'undefined') {
        const zip = new JSZip();

        // Add files to ZIP
        for (const [path, data] of Object.entries(structure)) {
          zip.file(path, data.content);
        }

        // Add main README if provided
        if (readmeContent) {
          zip.file('README.md', readmeContent);
        }

        return await zip.generateAsync({ type: 'blob' });
      }

      // Fallback to mock implementation
      console.warn('[ExportSystem] JSZip not available, using mock implementation');

      let zipContent = 'Mock ZIP Archive\n';
      zipContent += `Created: ${new Date().toISOString()}\n`;
      zipContent += `Files: ${Object.keys(structure).length}\n\n`;

      if (readmeContent) {
        zipContent += 'Main README:\n' + readmeContent + '\n\n';
      }

      zipContent += 'Files:\n';
      for (const [path, data] of Object.entries(structure)) {
        zipContent += `- ${path}\n`;
        zipContent += `  Content: ${data.content.substring(0, 100)}...\n`;
      }

      return new Blob([zipContent], { type: 'application/zip' });

    } catch (error) {
      console.error('[ExportSystem] Failed to create ZIP archive:', error);
      throw error;
    }
  }

  /**
   * Generate project README
   * @param {Object} project - Project object
   * @param {Array} artifacts - Project artifacts
   * @returns {string} README content
   */
  async generateProjectReadme(project, artifacts) {
    let readme = `# ${project.name}\n\n`;

    if (project.description) {
      readme += `${project.description}\n\n`;
    }

    readme += `## Overview\n\n`;
    readme += `- **Artifacts**: ${artifacts.length}\n`;
    readme += `- **Total Words**: ${artifacts.reduce((sum, a) => sum + (a.wordCount || 0), 0)}\n`;
    readme += `- **Created**: ${new Date(project.createdAt).toLocaleDateString()}\n`;
    readme += `- **Last Modified**: ${new Date(project.updatedAt).toLocaleDateString()}\n\n`;

    readme += `## Artifacts\n\n`;
    artifacts.forEach(artifact => {
      const fileName = this.generateFileName(artifact.name, '.md');
      readme += `- [${artifact.name}](${fileName}) - ${artifact.wordCount || 0} words\n`;
    });

    readme += `\n## Export Information\n\n`;
    readme += `- **Exported**: ${new Date().toISOString()}\n`;
    readme += `- **Project ID**: ${project.id}\n`;

    return readme;
  }

  /**
   * Generate main README for complete export
   * @param {Array} projects - All projects
   * @returns {string} Main README content
   */
  async generateMainReadme(projects) {
    let readme = `# Complete Export - Coder Companion\n\n`;
    readme += `Export created: ${new Date().toISOString()}\n\n`;

    readme += `## Summary\n\n`;
    readme += `- **Projects**: ${projects.length}\n`;

    const artifacts = await this.storage.get('artifacts', []);
    readme += `- **Total Artifacts**: ${artifacts.length}\n`;
    readme += `- **Total Words**: ${artifacts.reduce((sum, a) => sum + (a.wordCount || 0), 0)}\n\n`;

    readme += `## Projects\n\n`;
    projects.forEach(project => {
      const projectSlug = this.slugGenerator.generate(project.name);
      readme += `- [${project.name}](${projectSlug}/)\n`;
    });

    readme += `\n## Notes\n\n`;
    readme += `- Each project is contained in its own folder\n`;
    readme += `- Artifacts are exported as individual markdown files\n`;
    readme += `- Front-matter metadata is included where applicable\n`;
    readme += `- Version history is not included in exports\n`;

    return readme;
  }

  // Export Validation

  /**
   * Validate export options
   * @param {Object} options - Options to validate
   * @throws {Error} If options are invalid
   */
  validateExportOptions(options) {
    const validExtensions = ['.md', '.txt', '.json'];
    const validDateFormats = ['YYYY-MM-DD', 'MM/DD/YYYY', 'DD-MM-YYYY'];

    if (options.fileExtension && !validExtensions.includes(options.fileExtension)) {
      throw new Error(`Invalid file extension: ${options.fileExtension}`);
    }

    if (options.dateFormat && !validDateFormats.includes(options.dateFormat)) {
      throw new Error(`Invalid date format: ${options.dateFormat}`);
    }

    if (options.compressionLevel !== undefined &&
        (options.compressionLevel < 0 || options.compressionLevel > 9)) {
      throw new Error('Compression level must be between 0 and 9');
    }
  }

  /**
   * Preview export structure
   * @param {string} exportType - Type of export
   * @param {Object} selection - Selection criteria
   * @returns {Object} Preview structure
   */
  async previewExportStructure(exportType, selection) {
    try {
      let structure = {};

      switch (exportType) {
        case 'single-artifact':
          if (!selection.artifactId) throw new Error('Artifact ID required');
          structure = {
            type: 'single-artifact',
            fileName: await this.generateArtifactFileName(selection.artifactId),
            size: await this.estimateArtifactSize(selection.artifactId)
          };
          break;

        case 'project':
          if (!selection.projectId) throw new Error('Project ID required');
          structure = await this.previewProjectExport(selection.projectId);
          break;

        case 'all-projects':
          structure = await this.previewCompleteExport();
          break;

        default:
          throw new Error(`Unknown export type: ${exportType}`);
      }

      return structure;

    } catch (error) {
      console.error('[ExportSystem] Failed to preview export structure:', error);
      throw error;
    }
  }

  async generateArtifactFileName(artifactId) {
    const artifact = await this.artifactEditor.getArtifact(artifactId);
    return this.generateFileName(artifact.name, '.md');
  }

  async estimateArtifactSize(artifactId) {
    const artifact = await this.artifactEditor.getArtifact(artifactId);
    const content = await this.prepareMarkdownContent(artifact, true);
    return content.length;
  }

  async previewProjectExport(projectId) {
    const project = await this.projectManager.getProject(projectId);
    const artifacts = await this.storage.get('artifacts', []);
    const projectArtifacts = artifacts.filter(a => a.projectId === projectId);

    return {
      type: 'project',
      projectName: project.name,
      artifactCount: projectArtifacts.length,
      totalSize: await this.estimateProjectSize(projectId),
      structure: projectArtifacts.map(a => ({
        name: a.name,
        fileName: this.generateFileName(a.name, '.md'),
        size: a.content ? a.content.length : 0
      }))
    };
  }

  async previewCompleteExport() {
    const projects = await this.projectManager.getAllProjects();

    const structure = {};
    for (const project of projects) {
      structure[project.name] = await this.previewProjectExport(project.id);
    }

    return {
      type: 'complete',
      projectCount: projects.length,
      structure
    };
  }

  async estimateProjectSize(projectId) {
    const artifacts = await this.storage.get('artifacts', []);
    const projectArtifacts = artifacts.filter(a => a.projectId === projectId);

    let totalSize = 0;
    for (const artifact of projectArtifacts) {
      totalSize += (artifact.content || '').length;
    }

    return totalSize;
  }

  // Download Helpers

  /**
   * Download file content
   * @param {string} fileName - File name
   * @param {string|Blob} content - File content
   * @param {string} contentType - MIME type
   */
  downloadFile(fileName, content, contentType = 'text/plain') {
    try {
      // Use saveAs if available (file-saver.js)
      if (typeof saveAs !== 'undefined') {
        const blob = content instanceof Blob ? content : new Blob([content], { type: contentType });
        saveAs(blob, fileName);
        return;
      }

      // Fallback implementation
      const blob = content instanceof Blob ? content : new Blob([content], { type: contentType });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      a.style.display = 'none';

      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      URL.revokeObjectURL(url);

    } catch (error) {
      console.error('[ExportSystem] Failed to download file:', error);
      throw error;
    }
  }

  /**
   * Export and download artifact
   * @param {string} artifactId - Artifact ID
   * @param {Object} options - Export options
   */
  async exportAndDownloadArtifact(artifactId, options = {}) {
    try {
      const result = await this.exportArtifact(artifactId, options);
      this.downloadFile(result.fileName, result.content, result.contentType);
      return result;
    } catch (error) {
      console.error('[ExportSystem] Failed to export and download artifact:', error);
      throw error;
    }
  }

  /**
   * Export and download project
   * @param {string} projectId - Project ID
   * @param {Object} options - Export options
   */
  async exportAndDownloadProject(projectId, options = {}) {
    try {
      const result = await this.exportProject(projectId, options);
      this.downloadFile(result.fileName, result.content, result.contentType);
      return result;
    } catch (error) {
      console.error('[ExportSystem] Failed to export and download project:', error);
      throw error;
    }
  }

  /**
   * Export and download project as ZIP
   * @param {string} projectId - Project ID
   * @param {Object} options - Export options
   */
  async exportAndDownloadProjectAsZip(projectId, options = {}) {
    try {
      const result = await this.exportProjectAsZip(projectId, options);
      this.downloadFile(result.fileName, result.content, result.contentType);
      return result;
    } catch (error) {
      console.error('[ExportSystem] Failed to export and download project as ZIP:', error);
      throw error;
    }
  }

  /**
   * Export and download all projects as ZIP
   * @param {Object} options - Export options
   */
  async exportAndDownloadAllProjectsAsZip(options = {}) {
    try {
      const result = await this.exportAllProjectsAsZip(options);
      this.downloadFile(result.fileName, result.content, result.contentType);
      return result;
    } catch (error) {
      console.error('[ExportSystem] Failed to export and download all projects as ZIP:', error);
      throw error;
    }
  }

  // Clipboard Operations

  /**
   * Copy artifact content to clipboard
   * @param {string} artifactId - Artifact ID
   * @param {Object} options - Copy options
   * @returns {boolean} Success status
   */
  async copyArtifactToClipboard(artifactId, options = {}) {
    try {
      const artifact = await this.artifactEditor.getArtifact(artifactId);
      if (!artifact) {
        throw new Error('Artifact not found');
      }

      const { includeMetadata = false } = options;
      let content = artifact.content || '';

      if (includeMetadata) {
        content = await this.prepareMarkdownContent(artifact, true);
      }

      await this.copyToClipboard(content);

      console.log('[ExportSystem] Artifact copied to clipboard:', artifactId);
      return true;

    } catch (error) {
      console.error('[ExportSystem] Failed to copy artifact to clipboard:', error);
      return false;
    }
  }

  /**
   * Copy text to clipboard
   * @param {string} text - Text to copy
   * @returns {boolean} Success status
   */
  async copyToClipboard(text) {
    try {
      if (navigator.clipboard && window.isSecureContext) {
        // Use modern clipboard API
        await navigator.clipboard.writeText(text);
      } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        const success = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (!success) {
          throw new Error('Fallback clipboard copy failed');
        }
      }

      return true;

    } catch (error) {
      console.error('[ExportSystem] Failed to copy to clipboard:', error);
      return false;
    }
  }

  // Export Templates

  /**
   * Get export templates
   * @returns {Object} Available templates
   */
  getExportTemplates() {
    return {
      'simple-markdown': {
        name: 'Simple Markdown',
        description: 'Clean markdown without front-matter',
        options: {
          includeMetadata: false,
          includeVersions: false
        }
      },
      'with-metadata': {
        name: 'With Metadata',
        description: 'Markdown with YAML front-matter metadata',
        options: {
          includeMetadata: true,
          includeVersions: false
        }
      },
      'complete-export': {
        name: 'Complete Export',
        description: 'Full project export with all artifacts and metadata',
        options: {
          includeMetadata: true,
          includeVersions: true,
          includeEmptyFolders: false
        }
      },
      'backup-export': {
        name: 'Backup Export',
        description: 'Complete backup with versions and system data',
        options: {
          includeMetadata: true,
          includeVersions: true,
          includeEmptyFolders: true,
          compressionLevel: 9
        }
      }
    };
  }

  /**
   * Export using template
   * @param {string} templateName - Template name
   * @param {string} exportType - Type of export
   * @param {Object} selection - Selection criteria
   * @returns {Object} Export result
   */
  async exportWithTemplate(templateName, exportType, selection) {
    try {
      const templates = this.getExportTemplates();

      if (!templates[templateName]) {
        throw new Error(`Template not found: ${templateName}`);
      }

      const template = templates[templateName];
      const options = { ...template.options };

      let result;

      switch (exportType) {
        case 'artifact':
          result = await this.exportArtifact(selection.artifactId, options);
          break;
        case 'project':
          result = await this.exportProject(selection.projectId, options);
          break;
        case 'project-zip':
          result = await this.exportProjectAsZip(selection.projectId, options);
          break;
        case 'all-projects-zip':
          result = await this.exportAllProjectsAsZip(options);
          break;
        default:
          throw new Error(`Unknown export type: ${exportType}`);
      }

      return {
        ...result,
        template: templateName,
        templateDescription: template.description
      };

    } catch (error) {
      console.error('[ExportSystem] Failed to export with template:', error);
      throw error;
    }
  }

  // Export History

  /**
   * Save export to history
   * @param {Object} exportResult - Export result object
   * @returns {Object} History entry
   */
  async saveExportToHistory(exportResult) {
    try {
      const historyEntry = {
        id: `export_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date().toISOString(),
        fileName: exportResult.fileName,
        contentType: exportResult.contentType,
        size: exportResult.size,
        metadata: exportResult.metadata,
        template: exportResult.template || 'custom'
      };

      const history = await this.storage.get('exportHistory', []);
      history.push(historyEntry);

      // Keep only last 50 exports in history
      if (history.length > 50) {
        history.splice(0, history.length - 50);
      }

      await this.storage.set('exportHistory', history);

      console.log('[ExportSystem] Export saved to history:', historyEntry.id);
      return historyEntry;

    } catch (error) {
      console.error('[ExportSystem] Failed to save export to history:', error);
      throw error;
    }
  }

  /**
   * Get export history
   * @param {number} limit - Maximum number of entries to return
   * @returns {Array} Export history
   */
  async getExportHistory(limit = 20) {
    try {
      const history = await this.storage.get('exportHistory', []);
      return history
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, limit);

    } catch (error) {
      console.error('[ExportSystem] Failed to get export history:', error);
      return [];
    }
  }

  /**
   * Clear export history
   * @returns {boolean} Success status
   */
  async clearExportHistory() {
    try {
      await this.storage.remove('exportHistory');
      console.log('[ExportSystem] Export history cleared');
      return true;
    } catch (error) {
      console.error('[ExportSystem] Failed to clear export history:', error);
      return false;
    }
  }
}

// Export for use in other modules
const exportSystem = new ExportSystem();