// extension/modules/persona-workflow.js - Persona lifecycle and workflow management

class PersonaWorkflow {
  constructor() {
    this.storage = null;
    this.slugGenerator = null;
    this.PERSONAS_KEY = 'personas';
    this.ACTIVE_PERSONA_KEY = 'activePersona';
    this.CONTEXT_KEY_PREFIX = 'persona_context_';
    this.init();
  }

  async init() {
    console.log('[PersonaWorkflow] Initializing...');

    // Dynamically import utility modules
    if (typeof importScripts !== 'undefined') {
      // For content script environment
      this.storage = window.storageManager;
      this.slugGenerator = window.slugGenerator;
    } else {
      // For other environments, create instances
      this.storage = await this.loadStorageModule();
      this.slugGenerator = await this.loadSlugGeneratorModule();
    }
  }

  async loadStorageModule() {
    return {
      get: async (key, defaultValue = null) => {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : defaultValue;
      },
      set: async (key, value) => {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
      },
      remove: async (key) => {
        localStorage.removeItem(key);
        return true;
      }
    };
  }

  async loadSlugGeneratorModule() {
    return {
      generate: (text, options = {}) => {
        return text
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/^-+|-+$/g, '');
      }
    };
  }

  // Persona Lifecycle Management

  /**
   * Create a new persona
   * @param {Object} data - Persona data
   * @returns {Object} Created persona object
   */
  async createPersona(data) {
    console.log('[PersonaWorkflow] Creating persona:', data.name || 'Unnamed');

    try {
      const personaData = {
        id: this.generatePersonaId(),
        name: (data.name || 'New Persona').trim(),
        description: (data.description || '').trim(),
        slug: this.generatePersonaSlug(data.name || 'New Persona'),
        role: data.role || 'assistant',
        expertise: data.expertise || [],
        personality: data.personality || 'professional',
        systemPrompt: data.systemPrompt || '',
        temperature: data.temperature || 0.7,
        maxTokens: data.maxTokens || 1000,
        contextWindow: data.contextWindow || 4096,
        tags: data.tags || [],
        avatar: data.avatar || null,
        color: data.color || this.generateRandomColor(),
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastUsed: null,
        usageCount: 0,
        metadata: data.metadata || {}
      };

      // Validate persona data
      this.validatePersonaData(personaData);

      // Get existing personas
      const personas = await this.getAllPersonas(true);

      // Check for duplicate slugs
      const existingSlugs = personas.map(p => p.slug);
      if (existingSlugs.includes(personaData.slug)) {
        personaData.slug = `${personaData.slug}-${Date.now()}`;
      }

      // Add to personas array
      personas.push(personaData);

      // Save updated personas
      await this.storage.set(this.PERSONAS_KEY, personas);

      console.log('[PersonaWorkflow] Persona created successfully:', personaData.id);
      return personaData;

    } catch (error) {
      console.error('[PersonaWorkflow] Failed to create persona:', error);
      throw new Error(`Failed to create persona: ${error.message}`);
    }
  }

  /**
   * Get a persona by ID
   * @param {string} personaId - Persona ID
   * @returns {Object|null} Persona object or null if not found
   */
  async getPersona(personaId) {
    try {
      const personas = await this.getAllPersonas(true);
      return personas.find(p => p.id === personaId) || null;
    } catch (error) {
      console.error('[PersonaWorkflow] Failed to get persona:', error);
      return null;
    }
  }

  /**
   * Get all personas
   * @param {boolean} includeInactive - Whether to include inactive personas
   * @returns {Array} Array of persona objects
   */
  async getAllPersonas(includeInactive = false) {
    try {
      const personas = await this.storage.get(this.PERSONAS_KEY, []);

      if (!Array.isArray(personas)) {
        console.warn('[PersonaWorkflow] Personas data is not an array, resetting');
        return [];
      }

      if (includeInactive) {
        return personas;
      }

      return personas.filter(p => p.status === 'active');
    } catch (error) {
      console.error('[PersonaWorkflow] Failed to get personas:', error);
      return [];
    }
  }

  /**
   * Update a persona
   * @param {string} personaId - Persona ID
   * @param {Object} updates - Updates to apply
   * @returns {Object|null} Updated persona or null if not found
   */
  async updatePersona(personaId, updates) {
    console.log('[PersonaWorkflow] Updating persona:', personaId);

    try {
      const personas = await this.getAllPersonas(true);
      const personaIndex = personas.findIndex(p => p.id === personaId);

      if (personaIndex === -1) {
        throw new Error('Persona not found');
      }

      // Apply updates
      const updatedPersona = {
        ...personas[personaIndex],
        ...updates,
        updatedAt: new Date().toISOString()
      };

      // Validate updated persona
      this.validatePersonaData(updatedPersona);

      // Update personas array
      personas[personaIndex] = updatedPersona;
      await this.storage.set(this.PERSONAS_KEY, personas);

      console.log('[PersonaWorkflow] Persona updated successfully:', personaId);
      return updatedPersona;

    } catch (error) {
      console.error('[PersonaWorkflow] Failed to update persona:', error);
      throw error;
    }
  }

  /**
   * Delete a persona
   * @param {string} personaId - Persona ID
   * @returns {boolean} Success status
   */
  async deletePersona(personaId) {
    console.log('[PersonaWorkflow] Deleting persona:', personaId);

    try {
      const personas = await this.getAllPersonas(true);
      const filteredPersonas = personas.filter(p => p.id !== personaId);

      if (filteredPersonas.length === personas.length) {
        throw new Error('Persona not found');
      }

      // Check if this was the active persona
      const activePersona = await this.getActivePersona();
      if (activePersona && activePersona.id === personaId) {
        await this.clearActivePersona();
      }

      await this.storage.set(this.PERSONAS_KEY, filteredPersonas);

      // Clear persona context
      await this.clearPersonaContext(personaId);

      console.log('[PersonaWorkflow] Persona deleted successfully:', personaId);
      return true;

    } catch (error) {
      console.error('[PersonaWorkflow] Failed to delete persona:', error);
      return false;
    }
  }

  /**
   * Duplicate a persona
   * @param {string} personaId - Persona ID to duplicate
   * @param {Object} options - Duplication options
   * @returns {Object|null} Duplicated persona or null if source not found
   */
  async duplicatePersona(personaId, options = {}) {
    console.log('[PersonaWorkflow] Duplicating persona:', personaId);

    try {
      const sourcePersona = await this.getPersona(personaId);
      if (!sourcePersona) {
        throw new Error('Source persona not found');
      }

      const duplicateData = {
        name: options.name || `${sourcePersona.name} (Copy)`,
        description: sourcePersona.description,
        role: sourcePersona.role,
        expertise: [...sourcePersona.expertise],
        personality: sourcePersona.personality,
        systemPrompt: sourcePersona.systemPrompt,
        temperature: sourcePersona.temperature,
        maxTokens: sourcePersona.maxTokens,
        contextWindow: sourcePersona.contextWindow,
        tags: [...sourcePersona.tags],
        avatar: sourcePersona.avatar,
        color: sourcePersona.color,
        metadata: { ...sourcePersona.metadata, duplicatedFrom: personaId }
      };

      const duplicatedPersona = await this.createPersona(duplicateData);

      console.log('[PersonaWorkflow] Persona duplicated successfully:', duplicatedPersona.id);
      return duplicatedPersona;

    } catch (error) {
      console.error('[PersonaWorkflow] Failed to duplicate persona:', error);
      throw error;
    }
  }

  // Workflow Management

  /**
   * Set the active persona
   * @param {string} personaId - Persona ID to set as active
   * @returns {boolean} Success status
   */
  async setActivePersona(personaId) {
    console.log('[PersonaWorkflow] Setting active persona:', personaId);

    try {
      const persona = await this.getPersona(personaId);
      if (!persona) {
        throw new Error('Persona not found');
      }

      // Update persona usage
      await this.updatePersona(personaId, {
        lastUsed: new Date().toISOString(),
        usageCount: (persona.usageCount || 0) + 1
      });

      // Set as active
      await this.storage.set(this.ACTIVE_PERSONA_KEY, personaId);

      console.log('[PersonaWorkflow] Active persona set successfully:', personaId);
      return true;

    } catch (error) {
      console.error('[PersonaWorkflow] Failed to set active persona:', error);
      return false;
    }
  }

  /**
   * Get the active persona
   * @returns {Object|null} Active persona object or null if none
   */
  async getActivePersona() {
    try {
      const activePersonaId = await this.storage.get(this.ACTIVE_PERSONA_KEY);
      if (!activePersonaId) {
        return null;
      }

      return await this.getPersona(activePersonaId);
    } catch (error) {
      console.error('[PersonaWorkflow] Failed to get active persona:', error);
      return null;
    }
  }

  /**
   * Clear the active persona
   * @returns {boolean} Success status
   */
  async clearActivePersona() {
    try {
      await this.storage.remove(this.ACTIVE_PERSONA_KEY);
      console.log('[PersonaWorkflow] Active persona cleared');
      return true;
    } catch (error) {
      console.error('[PersonaWorkflow] Failed to clear active persona:', error);
      return false;
    }
  }

  // Context Management

  /**
   * Save persona context
   * @param {string} personaId - Persona ID
   * @param {Object} context - Context data to save
   * @returns {boolean} Success status
   */
  async savePersonaContext(personaId, context) {
    try {
      const contextKey = `${this.CONTEXT_KEY_PREFIX}${personaId}`;
      const contextData = {
        personaId,
        context,
        savedAt: new Date().toISOString(),
        version: 1
      };

      await this.storage.set(contextKey, contextData);
      console.log('[PersonaWorkflow] Persona context saved:', personaId);
      return true;

    } catch (error) {
      console.error('[PersonaWorkflow] Failed to save persona context:', error);
      return false;
    }
  }

  /**
   * Get persona context
   * @param {string} personaId - Persona ID
   * @returns {Object|null} Context data or null if not found
   */
  async getPersonaContext(personaId) {
    try {
      const contextKey = `${this.CONTEXT_KEY_PREFIX}${personaId}`;
      const contextData = await this.storage.get(contextKey);

      if (contextData && contextData.context) {
        console.log('[PersonaWorkflow] Persona context retrieved:', personaId);
        return contextData.context;
      }

      return null;

    } catch (error) {
      console.error('[PersonaWorkflow] Failed to get persona context:', error);
      return null;
    }
  }

  /**
   * Clear persona context
   * @param {string} personaId - Persona ID
   * @returns {boolean} Success status
   */
  async clearPersonaContext(personaId) {
    try {
      const contextKey = `${this.CONTEXT_KEY_PREFIX}${personaId}`;
      await this.storage.remove(contextKey);
      console.log('[PersonaWorkflow] Persona context cleared:', personaId);
      return true;

    } catch (error) {
      console.error('[PersonaWorkflow] Failed to clear persona context:', error);
      return false;
    }
  }

  // Prompt Processing

  /**
   * Process a prompt with a specific persona
   * @param {string} prompt - User prompt
   * @param {string} personaId - Persona ID to use
   * @returns {Object} Processed prompt data
   */
  async processPromptWithPersona(prompt, personaId) {
    console.log('[PersonaWorkflow] Processing prompt with persona:', personaId);

    try {
      const persona = await this.getPersona(personaId);
      if (!persona) {
        throw new Error('Persona not found');
      }

      // Get persona context
      const context = await this.getPersonaContext(personaId);

      // Build system prompt with persona information
      const systemPrompt = this.buildSystemPrompt(persona, context);

      // Process the prompt
      const processedPrompt = {
        systemPrompt,
        userPrompt: prompt,
        persona: {
          id: persona.id,
          name: persona.name,
          role: persona.role
        },
        context: context,
        parameters: {
          temperature: persona.temperature,
          maxTokens: persona.maxTokens,
          contextWindow: persona.contextWindow
        },
        timestamp: new Date().toISOString()
      };

      // Update persona usage
      await this.updatePersona(personaId, {
        lastUsed: new Date().toISOString(),
        usageCount: (persona.usageCount || 0) + 1
      });

      console.log('[PersonaWorkflow] Prompt processed successfully with persona:', personaId);
      return processedPrompt;

    } catch (error) {
      console.error('[PersonaWorkflow] Failed to process prompt with persona:', error);
      throw error;
    }
  }

  /**
   * Build system prompt with persona information
   * @param {Object} persona - Persona object
   * @param {Object} context - Context data
   * @returns {string} System prompt
   */
  buildSystemPrompt(persona, context = null) {
    let systemPrompt = persona.systemPrompt || '';

    // Add persona information
    const personaInfo = `You are ${persona.name}, a ${persona.role}.`;

    if (persona.description) {
      personaInfo += ` ${persona.description}`;
    }

    if (persona.expertise && persona.expertise.length > 0) {
      personaInfo += ` You have expertise in: ${persona.expertise.join(', ')}.`;
    }

    if (persona.personality) {
      personaInfo += ` Your personality is ${persona.personality}.`;
    }

    // Add context if available
    let contextInfo = '';
    if (context && context.recentTopics) {
      contextInfo += ` Recent topics discussed: ${context.recentTopics.join(', ')}.`;
    }

    if (context && context.userPreferences) {
      contextInfo += ` User preferences: ${context.userPreferences}.`;
    }

    // Combine all parts
    const fullPrompt = [personaInfo, contextInfo, systemPrompt]
      .filter(part => part.trim())
      .join('\n\n');

    return fullPrompt.trim();
  }

  // Utility Methods

  /**
   * Generate a unique persona ID
   * @returns {string} Unique persona ID
   */
  generatePersonaId() {
    return `persona_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate a persona slug
   * @param {string} name - Persona name
   * @returns {string} Generated slug
   */
  generatePersonaSlug(name) {
    if (this.slugGenerator && typeof this.slugGenerator.generate === 'function') {
      return this.slugGenerator.generate(name);
    }
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  /**
   * Generate a random color for persona
   * @returns {string} Hex color code
   */
  generateRandomColor() {
    const colors = [
      '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6',
      '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  }

  /**
   * Validate persona data
   * @param {Object} personaData - Persona data to validate
   * @throws {Error} If validation fails
   */
  validatePersonaData(personaData) {
    if (!personaData.name || typeof personaData.name !== 'string') {
      throw new Error('Persona name is required and must be a string');
    }

    if (personaData.name.trim().length === 0) {
      throw new Error('Persona name cannot be empty');
    }

    if (personaData.name.length > 100) {
      throw new Error('Persona name cannot exceed 100 characters');
    }

    if (personaData.description && personaData.description.length > 500) {
      throw new Error('Persona description cannot exceed 500 characters');
    }

    const validRoles = ['assistant', 'expert', 'reviewer', 'mentor', 'analyst'];
    if (personaData.role && !validRoles.includes(personaData.role)) {
      throw new Error(`Invalid persona role: ${personaData.role}`);
    }

    const validPersonalities = ['professional', 'friendly', 'technical', 'creative', 'concise'];
    if (personaData.personality && !validPersonalities.includes(personaData.personality)) {
      throw new Error(`Invalid persona personality: ${personaData.personality}`);
    }

    if (personaData.temperature !== undefined &&
        (personaData.temperature < 0 || personaData.temperature > 2)) {
      throw new Error('Temperature must be between 0 and 2');
    }

    if (personaData.maxTokens !== undefined &&
        (personaData.maxTokens < 1 || personaData.maxTokens > 4000)) {
      throw new Error('Max tokens must be between 1 and 4000');
    }

    const validStatuses = ['active', 'inactive'];
    if (personaData.status && !validStatuses.includes(personaData.status)) {
      throw new Error(`Invalid persona status: ${personaData.status}`);
    }

    if (personaData.expertise && !Array.isArray(personaData.expertise)) {
      throw new Error('Expertise must be an array');
    }

    if (personaData.tags && !Array.isArray(personaData.tags)) {
      throw new Error('Tags must be an array');
    }
  }

  /**
   * Search personas
   * @param {string} query - Search query
   * @param {Object} filters - Additional filters
   * @returns {Array} Array of matching personas
   */
  async searchPersonas(query, filters = {}) {
    try {
      let personas = await this.getAllPersonas(filters.includeInactive);

      // Apply search query
      if (query && query.trim()) {
        const searchTerm = query.toLowerCase().trim();
        personas = personas.filter(persona =>
          persona.name.toLowerCase().includes(searchTerm) ||
          persona.description.toLowerCase().includes(searchTerm) ||
          persona.expertise.some(skill => skill.toLowerCase().includes(searchTerm)) ||
          persona.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
      }

      // Apply filters
      if (filters.role) {
        personas = personas.filter(p => p.role === filters.role);
      }

      if (filters.status) {
        personas = personas.filter(p => p.status === filters.status);
      }

      if (filters.expertise && filters.expertise.length > 0) {
        personas = personas.filter(p =>
          filters.expertise.some(skill => p.expertise.includes(skill))
        );
      }

      return personas;

    } catch (error) {
      console.error('[PersonaWorkflow] Failed to search personas:', error);
      return [];
    }
  }

  /**
   * Get persona statistics
   * @returns {Object} Persona statistics
   */
  async getPersonaStats() {
    try {
      const personas = await this.getAllPersonas(true);

      const stats = {
        total: personas.length,
        active: personas.filter(p => p.status === 'active').length,
        inactive: personas.filter(p => p.status === 'inactive').length,
        byRole: {},
        byPersonality: {},
        totalUsage: personas.reduce((sum, p) => sum + (p.usageCount || 0), 0)
      };

      // Count by role
      personas.forEach(persona => {
        stats.byRole[persona.role] = (stats.byRole[persona.role] || 0) + 1;
        stats.byPersonality[persona.personality] = (stats.byPersonality[persona.personality] || 0) + 1;
      });

      return stats;

    } catch (error) {
      console.error('[PersonaWorkflow] Failed to get persona stats:', error);
      return {
        total: 0,
        active: 0,
        inactive: 0,
        byRole: {},
        byPersonality: {},
        totalUsage: 0
      };
    }
  }
}

// Export for use in other modules
const personaWorkflow = new PersonaWorkflow();