// extension/modules/artifact-editor.js - Artifact lifecycle and version management

class ArtifactEditor {
  constructor() {
    this.storage = null;
    this.slugGenerator = null;
    this.yamlProcessor = null;
    this.ARTIFACTS_KEY = 'artifacts';
    this.VERSION_PREFIX = 'version_';
    this.AUTOSAVE_PREFIX = 'autosave_';
    this.autoSaveTimers = new Map();

    this.init();
  }

  async init() {
    console.log('[ArtifactEditor] Initializing...');

    // Dynamically import utility modules
    if (typeof importScripts !== 'undefined') {
      // For content script environment
      this.storage = window.storageManager;
      this.slugGenerator = window.slugGenerator;
      this.yamlProcessor = window.yamlProcessor;
    } else {
      // For other environments, create instances
      this.storage = await this.loadStorageModule();
      this.slugGenerator = await this.loadSlugGeneratorModule();
      this.yamlProcessor = await this.loadYAMLProcessorModule();
    }
  }

  async loadStorageModule() {
    return {
      get: async (key, defaultValue = null) => {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : defaultValue;
      },
      set: async (key, value) => {
        localStorage.setItem(key, JSON.stringify(value));
        return true;
      },
      remove: async (key) => {
        localStorage.removeItem(key);
        return true;
      }
    };
  }

  async loadSlugGeneratorModule() {
    return {
      generate: (text, options = {}) => {
        return text
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/^-+|-+$/g, '');
      }
    };
  }

  async loadYAMLProcessorModule() {
    return {
      parse: (content) => {
        return { frontMatter: {}, content };
      },
      inject: (content, frontMatter) => {
        return content;
      }
    };
  }

  // Artifact Lifecycle Management

  /**
   * Create a new artifact
   * @param {string} name - Artifact name
   * @param {string} content - Artifact content
   * @param {string} projectId - Associated project ID
   * @param {string} personaId - Associated persona ID
   * @param {Object} options - Additional options
   * @returns {Object} Created artifact object
   */
  async createArtifact(name, content = '', projectId = null, personaId = null, options = {}) {
    console.log('[ArtifactEditor] Creating artifact:', name);

    try {
      const artifactData = {
        id: this.generateArtifactId(),
        name: name.trim(),
        title: name.trim(),
        content: content,
        slug: this.generateArtifactSlug(name),
        projectId,
        personaId,
        status: options.status || 'draft',
        priority: options.priority || 'medium',
        tags: options.tags || [],
        wordCount: this.countWords(content),
        version: 1,
        versions: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastModified: new Date().toISOString(),
        metadata: options.metadata || {}
      };

      // Create initial version
      await this.createVersion(artifactData.id, content, 'Initial creation');

      // Validate artifact data
      this.validateArtifactData(artifactData);

      // Get existing artifacts
      const artifacts = await this.getAllArtifacts(true);

      // Check for duplicate slugs
      const existingSlugs = artifacts.map(a => a.slug);
      if (existingSlugs.includes(artifactData.slug)) {
        artifactData.slug = `${artifactData.slug}-${Date.now()}`;
      }

      // Add to artifacts array
      artifacts.push(artifactData);

      // Save updated artifacts
      await this.storage.set(this.ARTIFACTS_KEY, artifacts);

      console.log('[ArtifactEditor] Artifact created successfully:', artifactData.id);
      return artifactData;

    } catch (error) {
      console.error('[ArtifactEditor] Failed to create artifact:', error);
      throw new Error(`Failed to create artifact: ${error.message}`);
    }
  }

  /**
   * Get an artifact by ID
   * @param {string} artifactId - Artifact ID
   * @returns {Object|null} Artifact object or null if not found
   */
  async getArtifact(artifactId) {
    try {
      const artifacts = await this.getAllArtifacts(true);
      return artifacts.find(a => a.id === artifactId) || null;
    } catch (error) {
      console.error('[ArtifactEditor] Failed to get artifact:', error);
      return null;
    }
  }

  /**
   * Get all artifacts
   * @param {boolean} includeAll - Whether to include all artifacts regardless of status
   * @returns {Array} Array of artifact objects
   */
  async getAllArtifacts(includeAll = false) {
    try {
      const artifacts = await this.storage.get(this.ARTIFACTS_KEY, []);

      if (!Array.isArray(artifacts)) {
        console.warn('[ArtifactEditor] Artifacts data is not an array, resetting');
        return [];
      }

      if (includeAll) {
        return artifacts;
      }

      return artifacts.filter(a => a.status !== 'archived');
    } catch (error) {
      console.error('[ArtifactEditor] Failed to get artifacts:', error);
      return [];
    }
  }

  /**
   * Update an artifact
   * @param {string} artifactId - Artifact ID
   * @param {Object} updates - Updates to apply
   * @returns {Object|null} Updated artifact or null if not found
   */
  async updateArtifact(artifactId, updates, options = {}) {
    console.log('[ArtifactEditor] Updating artifact:', artifactId);

    try {
      const artifacts = await this.getAllArtifacts(true);
      const artifactIndex = artifacts.findIndex(a => a.id === artifactId);

      if (artifactIndex === -1) {
        throw new Error('Artifact not found');
      }

      const artifact = artifacts[artifactIndex];
      const {
        createVersion = true,
        changeSummary = 'Content updated',
        skipValidation = false
      } = options;

      // Create version before update if content changed
      if (updates.content && updates.content !== artifact.content && createVersion) {
        await this.createVersion(artifactId, artifact.content, changeSummary);
      }

      // Apply updates
      const updatedArtifact = {
        ...artifact,
        ...updates,
        updatedAt: new Date().toISOString(),
        lastModified: new Date().toISOString(),
        version: createVersion ? (artifact.version || 1) + 1 : artifact.version,
        wordCount: updates.content ? this.countWords(updates.content) : artifact.wordCount
      };

      // Validate updated artifact
      if (!skipValidation) {
        this.validateArtifactData(updatedArtifact);
      }

      // Update artifacts array
      artifacts[artifactIndex] = updatedArtifact;
      await this.storage.set(this.ARTIFACTS_KEY, artifacts);

      console.log('[ArtifactEditor] Artifact updated successfully:', artifactId);
      return updatedArtifact;

    } catch (error) {
      console.error('[ArtifactEditor] Failed to update artifact:', error);
      throw error;
    }
  }

  /**
   * Delete an artifact
   * @param {string} artifactId - Artifact ID
   * @returns {boolean} Success status
   */
  async deleteArtifact(artifactId) {
    console.log('[ArtifactEditor] Deleting artifact:', artifactId);

    try {
      const artifacts = await this.getAllArtifacts(true);
      const filteredArtifacts = artifacts.filter(a => a.id !== artifactId);

      if (filteredArtifacts.length === artifacts.length) {
        throw new Error('Artifact not found');
      }

      await this.storage.set(this.ARTIFACTS_KEY, filteredArtifacts);

      // Clean up versions
      await this.cleanupArtifactVersions(artifactId);

      console.log('[ArtifactEditor] Artifact deleted successfully:', artifactId);
      return true;

    } catch (error) {
      console.error('[ArtifactEditor] Failed to delete artifact:', error);
      return false;
    }
  }

  // Version Management

  /**
   * Create a new version of an artifact
   * @param {string} artifactId - Artifact ID
   * @param {string} content - Content to version
   * @param {string} changeSummary - Summary of changes
   * @returns {Object} Version object
   */
  async createVersion(artifactId, content, changeSummary = '') {
    console.log('[ArtifactEditor] Creating version for artifact:', artifactId);

    try {
      const versionId = this.generateVersionId();
      const version = {
        id: versionId,
        artifactId,
        content,
        changeSummary,
        timestamp: new Date().toISOString(),
        wordCount: this.countWords(content),
        version: await this.getNextVersionNumber(artifactId)
      };

      const versionKey = `${this.VERSION_PREFIX}${versionId}`;
      await this.storage.set(versionKey, version);

      // Update artifact's version list
      const artifact = await this.getArtifact(artifactId);
      if (artifact) {
        const versions = artifact.versions || [];
        versions.push(versionId);

        // Keep only last 10 versions to prevent storage bloat
        if (versions.length > 10) {
          const toRemove = versions.slice(0, versions.length - 10);
          for (const oldVersionId of toRemove) {
            await this.storage.remove(`${this.VERSION_PREFIX}${oldVersionId}`);
          }
          versions.splice(0, versions.length - 10);
        }

        await this.updateArtifact(artifactId, { versions }, { createVersion: false });
      }

      console.log('[ArtifactEditor] Version created successfully:', versionId);
      return version;

    } catch (error) {
      console.error('[ArtifactEditor] Failed to create version:', error);
      throw error;
    }
  }

  /**
   * Get version history for an artifact
   * @param {string} artifactId - Artifact ID
   * @returns {Array} Array of version objects
   */
  async getVersionHistory(artifactId) {
    try {
      const artifact = await this.getArtifact(artifactId);
      if (!artifact || !artifact.versions) {
        return [];
      }

      const versions = [];
      for (const versionId of artifact.versions) {
        const version = await this.storage.get(`${this.VERSION_PREFIX}${versionId}`);
        if (version) {
          versions.push(version);
        }
      }

      return versions.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

    } catch (error) {
      console.error('[ArtifactEditor] Failed to get version history:', error);
      return [];
    }
  }

  /**
   * Restore an artifact to a specific version
   * @param {string} artifactId - Artifact ID
   * @param {string} versionId - Version ID to restore
   * @returns {Object|null} Updated artifact or null if failed
   */
  async restoreVersion(artifactId, versionId) {
    console.log('[ArtifactEditor] Restoring artifact to version:', versionId);

    try {
      const version = await this.storage.get(`${this.VERSION_PREFIX}${versionId}`);
      if (!version) {
        throw new Error('Version not found');
      }

      const result = await this.updateArtifact(artifactId, {
        content: version.content,
        wordCount: version.wordCount
      }, {
        changeSummary: `Restored to version: ${version.changeSummary}`
      });

      console.log('[ArtifactEditor] Version restored successfully:', versionId);
      return result;

    } catch (error) {
      console.error('[ArtifactEditor] Failed to restore version:', error);
      throw error;
    }
  }

  /**
   * Generate diff between two versions
   * @param {string} versionId1 - First version ID
   * @param {string} versionId2 - Second version ID
   * @returns {Object} Diff object with changes
   */
  async generateDiff(versionId1, versionId2) {
    try {
      const version1 = await this.storage.get(`${this.VERSION_PREFIX}${versionId1}`);
      const version2 = await this.storage.get(`${this.VERSION_PREFIX}${versionId2}`);

      if (!version1 || !version2) {
        throw new Error('One or both versions not found');
      }

      const diff = this.computeDiff(version1.content, version2.content);

      return {
        version1: version1.id,
        version2: version2.id,
        timestamp1: version1.timestamp,
        timestamp2: version2.timestamp,
        changes: diff
      };

    } catch (error) {
      console.error('[ArtifactEditor] Failed to generate diff:', error);
      throw error;
    }
  }

  // Content Processing

  /**
   * Render markdown content to HTML
   * @param {string} content - Markdown content
   * @returns {string} Rendered HTML
   */
  renderMarkdown(content) {
    // Basic markdown rendering (in production, use marked.js or similar)
    if (!content) return '';

    let html = content;

    // Headers
    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

    // Bold
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

    // Italic
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Code blocks
    html = html.replace(/```(\w+)?\n([\s\S]*?)\n```/g, '<pre><code class="language-$1">$2</code></pre>');

    // Inline code
    html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

    // Lists
    html = html.replace(/^\d+\. (.*$)/gim, '<li>$1</li>');
    html = html.replace(/^\- (.*$)/gim, '<li>$1</li>');

    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>');

    // Line breaks
    html = html.replace(/\n/g, '<br>');

    return html;
  }

  /**
   * Inject YAML front-matter into content
   * @param {string} content - Content to inject front-matter into
   * @param {Object} metadata - Metadata to inject
   * @returns {string} Content with front-matter
   */
  async injectYAMLFrontMatter(content, metadata) {
    if (this.yamlProcessor && typeof this.yamlProcessor.inject === 'function') {
      return this.yamlProcessor.inject(content, metadata);
    }

    // Fallback implementation
    const yaml = Object.entries(metadata)
      .map(([key, value]) => `${key}: ${value}`)
      .join('\n');

    return `---\n${yaml}\n---\n\n${content}`;
  }

  /**
   * Validate markdown syntax
   * @param {string} content - Content to validate
   * @returns {Object} Validation result
   */
  validateMarkdownSyntax(content) {
    const issues = [];

    // Check for unclosed code blocks
    const codeBlockMatches = content.match(/```/g);
    if (codeBlockMatches && codeBlockMatches.length % 2 !== 0) {
      issues.push('Unclosed code block detected');
    }

    // Check for unclosed inline code
    const inlineCodeMatches = content.match(/`/g);
    if (inlineCodeMatches && inlineCodeMatches.length % 2 !== 0) {
      issues.push('Unclosed inline code detected');
    }

    // Check for unmatched brackets in links
    const linkMatches = content.match(/\[([^\]]*)\]\(([^)]*)\)/g);
    if (linkMatches) {
      linkMatches.forEach(match => {
        if (!match.includes('](') || !match.endsWith(')')) {
          issues.push('Malformed link detected');
        }
      });
    }

    return {
      valid: issues.length === 0,
      issues
    };
  }

  // Editor Features

  /**
   * Setup auto-save for an artifact
   * @param {string} artifactId - Artifact ID
   * @param {number} interval - Auto-save interval in milliseconds
   * @returns {Object} Auto-save controller
   */
  setupAutoSave(artifactId, interval = 2000) {
    console.log(`[ArtifactEditor] Setting up auto-save for ${artifactId} (${interval}ms)`);

    // Clear existing timer
    this.clearAutoSave(artifactId);

    let lastContent = '';
    let saveTimeout;

    const controller = {
      save: async (content) => {
        if (content === lastContent) return;

        clearTimeout(saveTimeout);
        saveTimeout = setTimeout(async () => {
          try {
            await this.updateArtifact(artifactId, { content }, {
              createVersion: false,
              skipValidation: true
            });
            lastContent = content;
            console.log(`[ArtifactEditor] Auto-saved ${artifactId}`);
          } catch (error) {
            console.error('[ArtifactEditor] Auto-save failed:', error);
          }
        }, interval);
      },

      destroy: () => {
        clearTimeout(saveTimeout);
        this.clearAutoSave(artifactId);
      }
    };

    this.autoSaveTimers.set(artifactId, controller);
    return controller;
  }

  /**
   * Clear auto-save for an artifact
   * @param {string} artifactId - Artifact ID
   */
  clearAutoSave(artifactId) {
    const controller = this.autoSaveTimers.get(artifactId);
    if (controller) {
      controller.destroy();
      this.autoSaveTimers.delete(artifactId);
    }
  }

  /**
   * Get content statistics
   * @param {string} content - Content to analyze
   * @returns {Object} Content statistics
   */
  getContentStatistics(content) {
    if (!content) {
      return {
        wordCount: 0,
        charCount: 0,
        lineCount: 0,
        paragraphCount: 0,
        readingTime: 0
      };
    }

    const words = content.trim().split(/\s+/).filter(word => word.length > 0);
    const chars = content.length;
    const lines = content.split('\n');
    const paragraphs = content.split('\n\n').filter(p => p.trim().length > 0);

    // Estimate reading time (words per minute)
    const wordsPerMinute = 200;
    const readingTime = Math.ceil(words.length / wordsPerMinute);

    return {
      wordCount: words.length,
      charCount: chars,
      lineCount: lines.length,
      paragraphCount: paragraphs.length,
      readingTime
    };
  }

  /**
   * Search and replace in content
   * @param {string} content - Content to search in
   * @param {string} searchTerm - Term to search for
   * @param {string} replacement - Replacement text
   * @param {Object} options - Search options
   * @returns {Object} Search and replace result
   */
  searchAndReplace(content, searchTerm, replacement, options = {}) {
    const {
      caseSensitive = false,
      wholeWord = false,
      regex = false
    } = options;

    if (!content || !searchTerm) {
      return {
        content,
        matches: 0,
        replacements: 0
      };
    }

    let searchPattern = searchTerm;
    let flags = 'g';

    if (!caseSensitive) {
      flags += 'i';
    }

    if (wholeWord) {
      searchPattern = `\\b${this.escapeRegExp(searchTerm)}\\b`;
    }

    if (!regex) {
      searchPattern = this.escapeRegExp(searchPattern);
    }

    try {
      const regex = new RegExp(searchPattern, flags);
      const matches = (content.match(regex) || []).length;
      const newContent = content.replace(regex, replacement);

      return {
        content: newContent,
        matches,
        replacements: matches
      };
    } catch (error) {
      console.error('[ArtifactEditor] Search and replace failed:', error);
      return {
        content,
        matches: 0,
        replacements: 0,
        error: error.message
      };
    }
  }

  // Utility Methods

  /**
   * Generate a unique artifact ID
   * @returns {string} Unique artifact ID
   */
  generateArtifactId() {
    return `artifact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate an artifact slug
   * @param {string} name - Artifact name
   * @returns {string} Generated slug
   */
  generateArtifactSlug(name) {
    if (this.slugGenerator && typeof this.slugGenerator.generate === 'function') {
      return this.slugGenerator.generate(name);
    }
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  /**
   * Generate a unique version ID
   * @returns {string} Unique version ID
   */
  generateVersionId() {
    return `version_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get next version number for an artifact
   * @param {string} artifactId - Artifact ID
   * @returns {number} Next version number
   */
  async getNextVersionNumber(artifactId) {
    const versions = await this.getVersionHistory(artifactId);
    return versions.length + 1;
  }

  /**
   * Count words in content
   * @param {string} content - Content to count words in
   * @returns {number} Word count
   */
  countWords(content) {
    if (!content) return 0;
    return content.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  /**
   * Validate artifact data
   * @param {Object} artifactData - Artifact data to validate
   * @throws {Error} If validation fails
   */
  validateArtifactData(artifactData) {
    if (!artifactData.name || typeof artifactData.name !== 'string') {
      throw new Error('Artifact name is required and must be a string');
    }

    if (artifactData.name.trim().length === 0) {
      throw new Error('Artifact name cannot be empty');
    }

    if (artifactData.name.length > 200) {
      throw new Error('Artifact name cannot exceed 200 characters');
    }

    if (artifactData.content && typeof artifactData.content !== 'string') {
      throw new Error('Artifact content must be a string');
    }

    const validStatuses = ['draft', 'in-progress', 'completed', 'archived'];
    if (artifactData.status && !validStatuses.includes(artifactData.status)) {
      throw new Error(`Invalid artifact status: ${artifactData.status}`);
    }

    const validPriorities = ['low', 'medium', 'high', 'urgent'];
    if (artifactData.priority && !validPriorities.includes(artifactData.priority)) {
      throw new Error(`Invalid artifact priority: ${artifactData.priority}`);
    }

    if (artifactData.tags && !Array.isArray(artifactData.tags)) {
      throw new Error('Tags must be an array');
    }
  }

  /**
   * Compute diff between two content strings
   * @param {string} oldContent - Old content
   * @param {string} newContent - New content
   * @returns {Array} Array of changes
   */
  computeDiff(oldContent, newContent) {
    // Simple line-by-line diff (in production, use a proper diff library)
    const oldLines = oldContent.split('\n');
    const newLines = newContent.split('\n');
    const changes = [];

    const maxLines = Math.max(oldLines.length, newLines.length);

    for (let i = 0; i < maxLines; i++) {
      const oldLine = oldLines[i] || '';
      const newLine = newLines[i] || '';

      if (oldLine !== newLine) {
        changes.push({
          line: i + 1,
          old: oldLine,
          new: newLine,
          type: oldLine ? (newLine ? 'modified' : 'deleted') : 'added'
        });
      }
    }

    return changes;
  }

  /**
   * Escape special regex characters
   * @param {string} string - String to escape
   * @returns {string} Escaped string
   */
  escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Clean up versions for a deleted artifact
   * @param {string} artifactId - Artifact ID
   */
  async cleanupArtifactVersions(artifactId) {
    try {
      const artifact = await this.getArtifact(artifactId);
      if (artifact && artifact.versions) {
        for (const versionId of artifact.versions) {
          await this.storage.remove(`${this.VERSION_PREFIX}${versionId}`);
        }
      }
    } catch (error) {
      console.error('[ArtifactEditor] Failed to cleanup versions:', error);
    }
  }

  /**
   * Search artifacts
   * @param {string} query - Search query
   * @param {Object} filters - Additional filters
   * @returns {Array} Array of matching artifacts
   */
  async searchArtifacts(query, filters = {}) {
    try {
      let artifacts = await this.getAllArtifacts(filters.includeArchived);

      // Apply search query
      if (query && query.trim()) {
        const searchTerm = query.toLowerCase().trim();
        artifacts = artifacts.filter(artifact =>
          artifact.name.toLowerCase().includes(searchTerm) ||
          artifact.content.toLowerCase().includes(searchTerm) ||
          artifact.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        );
      }

      // Apply filters
      if (filters.status) {
        artifacts = artifacts.filter(a => a.status === filters.status);
      }

      if (filters.priority) {
        artifacts = artifacts.filter(a => a.priority === filters.priority);
      }

      if (filters.projectId) {
        artifacts = artifacts.filter(a => a.projectId === filters.projectId);
      }

      if (filters.personaId) {
        artifacts = artifacts.filter(a => a.personaId === filters.personaId);
      }

      return artifacts;

    } catch (error) {
      console.error('[ArtifactEditor] Failed to search artifacts:', error);
      return [];
    }
  }

  /**
   * Get artifact statistics
   * @returns {Object} Artifact statistics
   */
  async getArtifactStats() {
    try {
      const artifacts = await this.getAllArtifacts(true);

      const stats = {
        total: artifacts.length,
        active: artifacts.filter(a => a.status !== 'archived').length,
        archived: artifacts.filter(a => a.status === 'archived').length,
        byStatus: {},
        byPriority: {},
        totalWords: artifacts.reduce((sum, a) => sum + (a.wordCount || 0), 0)
      };

      // Count by status and priority
      artifacts.forEach(artifact => {
        stats.byStatus[artifact.status] = (stats.byStatus[artifact.status] || 0) + 1;
        stats.byPriority[artifact.priority] = (stats.byPriority[artifact.priority] || 0) + 1;
      });

      return stats;

    } catch (error) {
      console.error('[ArtifactEditor] Failed to get artifact stats:', error);
      return {
        total: 0,
        active: 0,
        archived: 0,
        byStatus: {},
        byPriority: {},
        totalWords: 0
      };
    }
  }
}

// Export for use in other modules
const artifactEditor = new ArtifactEditor();