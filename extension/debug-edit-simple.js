// Simple debug script to trace edit functionality
console.log('🔍 Simple Edit Debug Loaded');

// Override console.log to capture all messages
const originalLog = console.log;
const logs = [];

console.log = function(...args) {
  logs.push(args.join(' '));
  originalLog.apply(console, args);
};

// Function to check current state
function checkEditState() {
  console.log('=== EDIT STATE CHECK ===');
  
  // 1. Check if CoderCompanionInjector exists
  console.log('1. CoderCompanionInjector exists:', typeof window.coderCompanionInjector !== 'undefined');
  
  if (typeof window.coderCompanionInjector !== 'undefined') {
    // 2. Check if edit methods exist
    const methods = [
      'handleEditItem',
      'openEditModalDirectly', 
      'showEditProjectModal',
      'communicateWithSidebar'
    ];
    
    methods.forEach(method => {
      console.log(`2. ${method} exists:`, typeof window.coderCompanionInjector[method] === 'function');
    });
  }
  
  // 3. Check for sidebar
  const sidebar = document.querySelector('#cc-sidebar');
  console.log('3. Sidebar element exists:', !!sidebar);
  
  if (sidebar) {
    const isVisible = sidebar.style.display !== 'none' && 
                     sidebar.style.visibility !== 'hidden' &&
                     !sidebar.classList.contains('hidden');
    console.log('3. Sidebar visible:', isVisible);
  }
  
  // 4. Check for existing modals
  const existingModal = document.querySelector('.cc-modal-overlay');
  console.log('4. Existing modal found:', !!existingModal);
  
  // 5. Check for edit buttons
  const editButtons = document.querySelectorAll('#edit-item-btn, [onclick*="Edit"]');
  console.log('5. Edit buttons found:', editButtons.length);
  
  console.log('=== END STATE CHECK ===');
}

// Function to simulate edit click
function simulateEditClick() {
  console.log('=== SIMULATING EDIT CLICK ===');
  
  if (typeof window.coderCompanionInjector === 'undefined') {
    console.log('❌ CoderCompanionInjector not available');
    return;
  }
  
  // Create a test item
  const testItem = {
    id: 'debug-test-123',
    name: 'Debug Test Item',
    description: 'This is a test item for debugging',
    status: 'active'
  };
  
  console.log('Test item created:', testItem);
  
  try {
    console.log('Calling handleEditItem...');
    window.coderCompanionInjector.handleEditItem(testItem, 'project');
    console.log('✅ handleEditItem called successfully');
    
    // Check if modal appeared after a short delay
    setTimeout(() => {
      const modal = document.querySelector('.cc-modal-overlay');
      console.log('Modal check after 500ms:', !!modal);
      
      if (modal) {
        console.log('✅ Modal found!');
        console.log('Modal content preview:', modal.innerHTML.substring(0, 200) + '...');
      } else {
        console.log('❌ No modal found after edit attempt');
      }
    }, 500);
    
  } catch (error) {
    console.log('❌ Error in handleEditItem:', error);
  }
  
  console.log('=== END SIMULATION ===');
}

// Function to manually create a test modal
function createTestModal() {
  console.log('=== CREATING TEST MODAL ===');
  
  if (typeof window.coderCompanionInjector === 'undefined') {
    console.log('❌ CoderCompanionInjector not available');
    return;
  }
  
  try {
    // Try to create a modal using the createModal method
    const modal = window.coderCompanionInjector.createModal('Test Modal', `
      <div style="padding: 20px;">
        <h3>Test Modal Content</h3>
        <p>If you can see this, modal creation works!</p>
        <button onclick="this.closest('.cc-modal-overlay').remove()">Close</button>
      </div>
    `);
    
    console.log('✅ Test modal created');
    console.log('Modal element:', modal);
    
    // Check if it's in the DOM
    const modalInDom = document.contains(modal);
    console.log('Modal in DOM:', modalInDom);
    
    if (!modalInDom) {
      console.log('⚠️ Modal created but not in DOM, adding manually...');
      document.body.appendChild(modal);
    }
    
  } catch (error) {
    console.log('❌ Error creating test modal:', error);
  }
  
  console.log('=== END TEST MODAL ===');
}

// Function to show recent logs
function showRecentLogs() {
  console.log('=== RECENT LOGS ===');
  const recentLogs = logs.slice(-20); // Last 20 logs
  recentLogs.forEach((log, index) => {
    console.log(`${index + 1}. ${log}`);
  });
  console.log('=== END LOGS ===');
}

// Make functions available globally
window.checkEditState = checkEditState;
window.simulateEditClick = simulateEditClick;
window.createTestModal = createTestModal;
window.showRecentLogs = showRecentLogs;

console.log('🧪 Simple Edit Debug Ready!');
console.log('Available functions:');
console.log('- checkEditState() - Check current state');
console.log('- simulateEditClick() - Simulate edit button click');
console.log('- createTestModal() - Create a test modal');
console.log('- showRecentLogs() - Show recent console logs');

// Auto-run state check
setTimeout(() => {
  console.log('🔄 Auto-running initial state check...');
  checkEditState();
}, 1000);
