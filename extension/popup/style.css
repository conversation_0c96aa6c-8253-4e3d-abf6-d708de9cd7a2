/* popup/style.css - Styling for Coder Companion popup */

:root {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;

  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;

  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;

  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);

  --border-radius: 8px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;

  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  width: 320px;
  min-height: 400px;
  max-height: 600px;
  overflow: hidden;
}

.popup-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 600px;
}

/* Header */
.popup-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
}

.header-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.logo {
  flex-shrink: 0;
}

.logo svg {
  color: white;
}

.header-text h1 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: white;
}

.subtitle {
  font-size: var(--font-size-sm);
  opacity: 0.9;
  color: white;
}

/* Status Section */
.status-section {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) 0;
}

.status-item:not(:last-child) {
  margin-bottom: var(--spacing-sm);
}

.status-icon {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--secondary-color);
}

.status-indicator.active {
  background-color: var(--success-color);
}

.status-text {
  flex: 1;
  font-size: var(--font-size-sm);
}

.status-label {
  color: var(--text-secondary);
  margin-right: var(--spacing-xs);
}

/* Actions Section */
.actions-section {
  padding: var(--spacing-lg);
  flex: 1;
  overflow-y: auto;
}

.actions-section h2 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-sm);
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-md);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-primary);
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: var(--font-size-sm);
  text-align: center;
  min-height: 80px;
  justify-content: center;
}

.action-button:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.action-button:active {
  transform: translateY(0);
}

.action-button.primary {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.action-button.primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.action-button svg {
  width: 20px;
  height: 20px;
}

/* Stats Section */
.stats-section {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

.stats-section h2 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-md);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-md);
  background-color: var(--bg-primary);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.stat-number {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Footer */
.popup-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-tertiary);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.footer-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.footer-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-secondary);
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  font-size: var(--font-size-sm);
}

.footer-button:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

.version-info {
  text-align: center;
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* Scrollbar Styling */
.actions-section::-webkit-scrollbar {
  width: 6px;
}

.actions-section::-webkit-scrollbar-track {
  background: transparent;
}

.actions-section::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.actions-section::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}

/* Responsive adjustments */
@media (max-width: 320px) {
  .actions-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* Animation for status indicator */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.status-indicator.active {
  animation: pulse 2s infinite;
}

/* Loading state */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}