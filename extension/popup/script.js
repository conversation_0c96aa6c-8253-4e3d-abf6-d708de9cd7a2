// popup/script.js - JavaScript for Coder Companion popup

class PopupManager {
  constructor() {
    this.extensionState = null;
    this.currentTab = null;
    this.init();
  }

  async init() {
    console.log('[Popup] Initializing popup...');

    try {
      // Get extension state and current tab info
      await this.loadExtensionState();
      await this.loadCurrentTabInfo();
      await this.loadStatistics();

      // Setup event listeners
      this.setupEventListeners();

      // Update UI
      this.updateUI();

      console.log('[Popup] Popup initialized successfully');
    } catch (error) {
      console.error('[Popup] Initialization failed:', error);
      this.showError('Failed to initialize popup');
    }
  }

  async loadExtensionState() {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ type: 'GET_EXTENSION_STATE' }, (response) => {
        if (response) {
          this.extensionState = response;
          resolve();
        } else {
          console.warn('[Popup] No extension state received');
          resolve();
        }
      });
    });
  }

  async loadCurrentTabInfo() {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage({ type: 'GET_ACTIVE_TAB_INFO' }, (response) => {
        if (response && !response.error) {
          this.currentTab = response;
        } else {
          console.warn('[Popup] Could not get tab info:', response?.error);
        }
        resolve();
      });
    });
  }

  async loadStatistics() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['dataSchema'], (result) => {
        if (result.dataSchema) {
          this.statistics = {
            projects: result.dataSchema.projects?.length || 0,
            personas: result.dataSchema.personas?.length || 0,
            artifacts: result.dataSchema.artifacts?.length || 0
          };
        } else {
          this.statistics = { projects: 0, personas: 0, artifacts: 0 };
        }
        resolve();
      });
    });
  }

  setupEventListeners() {
    // Open sidebar button
    document.getElementById('open-sidebar-btn').addEventListener('click', () => {
      this.openSidebar();
    });

    // New project button
    document.getElementById('new-project-btn').addEventListener('click', () => {
      this.createNewProject();
    });

    // Manage personas button
    document.getElementById('manage-personas-btn').addEventListener('click', () => {
      this.openPersonasManager();
    });

    // Export data button
    document.getElementById('export-data-btn').addEventListener('click', () => {
      this.openExportDialog();
    });

    // Settings button
    document.getElementById('settings-btn').addEventListener('click', () => {
      this.openSettings();
    });

    // Help button
    document.getElementById('help-btn').addEventListener('click', () => {
      this.openHelp();
    });
  }

  updateUI() {
    // Update extension status
    const statusText = document.getElementById('status-text');
    const statusIndicator = document.getElementById('status-indicator');

    if (this.extensionState?.isEnabled) {
      statusText.textContent = 'Active';
      statusIndicator.classList.add('active');
    } else {
      statusText.textContent = 'Inactive';
      statusIndicator.classList.remove('active');
    }

    // Update page status
    const pageText = document.getElementById('page-text');
    if (this.currentTab) {
      if (this.currentTab.url.includes('aistudio.google.com') ||
          this.currentTab.url.includes('makersuite.google.com')) {
        pageText.textContent = 'Google AI Studio';
      } else {
        pageText.textContent = 'Other Page';
      }
    } else {
      pageText.textContent = 'Unknown';
    }

    // Update version
    const versionText = document.getElementById('version-text');
    if (this.extensionState?.version) {
      versionText.textContent = this.extensionState.version;
    }

    // Update statistics
    if (this.statistics) {
      document.getElementById('projects-count').textContent = this.statistics.projects;
      document.getElementById('personas-count').textContent = this.statistics.personas;
      document.getElementById('artifacts-count').textContent = this.statistics.artifacts;
    }
  }

  async openSidebar() {
    console.log('[Popup] Opening sidebar...');

    // Send message to content script to open sidebar
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    if (tabs.length > 0) {
      const activeTab = tabs[0];
      console.log('[Popup] Active tab:', { id: activeTab.id, url: activeTab.url });

      // Check if we're on the correct domain
      if (activeTab.url.includes('aistudio.google.com') ||
          activeTab.url.includes('makersuite.google.com')) {

        console.log('[Popup] Sending OPEN_SIDEBAR message to tab:', activeTab.id);

        // First, ping the content script to check if it's ready
        console.log('[Popup] Pinging content script...');

        const pingWithRetry = (retries = 3, delay = 500) => {
          chrome.tabs.sendMessage(activeTab.id, {
            type: 'PING'
          }, (pingResponse) => {
            if (chrome.runtime.lastError) {
              const errorMsg = chrome.runtime.lastError.message || 'Unknown runtime error';
              console.error('[Popup] Content script not available:', errorMsg);

              if (retries > 0) {
                console.log(`[Popup] Retrying ping in ${delay}ms (${retries} retries left)`);
                setTimeout(() => pingWithRetry(retries - 1, delay * 2), delay);
              } else {
                this.showNotification(`Content script not loaded: ${errorMsg}. Please refresh the page.`);
              }
              return;
            }

            console.log('[Popup] Ping response:', pingResponse);
            if (pingResponse && pingResponse.type === 'PONG') {
              if (pingResponse.isReady) {
                console.log('[Popup] Content script is ready, sending OPEN_SIDEBAR message');

                // Add a timeout to detect if content script is not responding
                const messageTimeout = setTimeout(() => {
                  console.error('[Popup] Content script response timeout');
                  this.showNotification('Content script not responding. Please refresh the page.');
                }, 5000);

                chrome.tabs.sendMessage(activeTab.id, {
                  type: 'OPEN_SIDEBAR'
                }, (response) => {
                  clearTimeout(messageTimeout);

                  console.log('[Popup] Received response from content script:', response);

                  if (chrome.runtime.lastError) {
                    const errorMsg = chrome.runtime.lastError.message || 'Unknown runtime error';
                    console.error('[Popup] Chrome runtime error:', errorMsg);
                    this.showNotification(`Content script error: ${errorMsg}`);
                    return;
                  }

                  if (response?.success) {
                    console.log('[Popup] Sidebar opened successfully');
                    // Close popup after opening sidebar
                    window.close();
                  } else {
                    const errorMsg = response?.error || 'Content script not ready or not available';
                    console.warn('[Popup] Failed to open sidebar:', errorMsg);
                    this.showNotification(`Unable to open sidebar: ${errorMsg}`);
                  }
                });
              } else {
                console.warn('[Popup] Content script not ready yet');
                if (retries > 0) {
                  console.log(`[Popup] Content script not ready, retrying in ${delay}ms (${retries} retries left)`);
                  setTimeout(() => pingWithRetry(retries - 1, delay * 2), delay);
                } else {
                  this.showNotification('Content script not ready. Please wait a moment and try again.');
                }
              }
            } else {
              console.warn('[Popup] No response to ping');
              if (retries > 0) {
                console.log(`[Popup] No ping response, retrying in ${delay}ms (${retries} retries left)`);
                setTimeout(() => pingWithRetry(retries - 1, delay * 2), delay);
              } else {
                this.showNotification('Content script not responding. Please refresh the page.');
              }
            }
          });
        };

        pingWithRetry();
      } else {
        console.warn('[Popup] Not on Google AI Studio domain:', activeTab.url);
        this.showNotification('Please navigate to Google AI Studio to use the sidebar');
      }
    } else {
      console.error('[Popup] No active tab found');
      this.showNotification('No active tab found');
    }
  }

  createNewProject() {
    console.log('[Popup] Creating new project...');
    this.openSidebar();
    // Additional project creation logic will be implemented in Phase 5
  }

  openPersonasManager() {
    console.log('[Popup] Opening personas manager...');
    this.openSidebar();
    // Additional personas management logic will be implemented in Phase 5
  }

  openExportDialog() {
    console.log('[Popup] Opening export dialog...');
    this.openSidebar();
    // Additional export logic will be implemented in Phase 4
  }

  openSettings() {
    console.log('[Popup] Opening settings...');
    // For now, open sidebar - settings will be implemented in Phase 5
    this.openSidebar();
  }

  openHelp() {
    console.log('[Popup] Opening help...');
    // Open help documentation or tutorial
    chrome.tabs.create({
      url: 'https://github.com/your-repo/coder-companion#readme'
    });
  }

  showNotification(message) {
    // Create a simple notification within the popup
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      left: 20px;
      right: 20px;
      background: var(--primary-color);
      color: white;
      padding: 12px;
      border-radius: 8px;
      z-index: 1000;
      font-size: 14px;
      text-align: center;
      animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  showError(message) {
    const errorElement = document.createElement('div');
    errorElement.className = 'error-message';
    errorElement.style.cssText = `
      position: fixed;
      top: 20px;
      left: 20px;
      right: 20px;
      background: var(--error-color);
      color: white;
      padding: 12px;
      border-radius: 8px;
      z-index: 1000;
      font-size: 14px;
      text-align: center;
    `;
    errorElement.textContent = message;

    document.body.appendChild(errorElement);
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PopupManager();
});

// Add CSS for notifications
const style = document.createElement('style');
style.textContent = `
  @keyframes slideIn {
    from {
      transform: translateY(-100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes slideOut {
    from {
      transform: translateY(0);
      opacity: 1;
    }
    to {
      transform: translateY(-100%);
      opacity: 0;
    }
  }
`;
document.head.appendChild(style);