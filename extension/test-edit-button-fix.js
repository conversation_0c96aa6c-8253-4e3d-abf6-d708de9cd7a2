// Test script to verify the edit button fix
// This script can be run in the browser console to test the edit button functionality

console.log('🧪 Testing Edit Button Fix...');

// Test 1: Simulate the showItemDetailsModal call
function testShowItemDetailsModal() {
  console.log('\n📋 Test 1: showItemDetailsModal');
  
  if (typeof window.coderCompanionInjector !== 'undefined') {
    console.log('✅ CoderCompanionInjector available');
    
    // Create a mock item
    const mockItem = {
      id: 'test-project-123',
      name: 'Test Project',
      description: 'A test project for edit functionality',
      status: 'active',
      createdAt: new Date().toISOString(),
      lastModified: new Date().toLocaleDateString()
    };
    
    try {
      // Test showing the modal
      window.coderCompanionInjector.showItemDetailsModal('project', mockItem);
      console.log('✅ Modal created successfully');
      
      // Check if modal exists
      const modal = document.getElementById('cc-modal');
      if (modal) {
        console.log('✅ Modal element found');
        
        // Check if data attributes are set
        const itemType = modal.getAttribute('data-item-type');
        const itemId = modal.getAttribute('data-item-id');
        const itemData = modal.getAttribute('data-item-data');
        
        if (itemType === 'project') {
          console.log('✅ Item type stored correctly');
        } else {
          console.log('❌ Item type not stored correctly');
        }
        
        if (itemId === 'test-project-123') {
          console.log('✅ Item ID stored correctly');
        } else {
          console.log('❌ Item ID not stored correctly');
        }
        
        if (itemData) {
          try {
            const parsedData = JSON.parse(itemData);
            if (parsedData.name === 'Test Project') {
              console.log('✅ Item data stored correctly');
            } else {
              console.log('❌ Item data not stored correctly');
            }
          } catch (e) {
            console.log('❌ Item data not valid JSON');
          }
        } else {
          console.log('❌ Item data not stored');
        }
        
        // Check if edit button exists
        const editButton = modal.querySelector('#edit-item-btn');
        if (editButton) {
          console.log('✅ Edit button found');
          
          // Test clicking the edit button (this should not throw an error)
          try {
            console.log('🔄 Testing edit button click...');
            editButton.click();
            console.log('✅ Edit button click successful - no ReferenceError');
          } catch (error) {
            if (error instanceof ReferenceError) {
              console.log('❌ ReferenceError still occurs:', error.message);
            } else {
              console.log('⚠️ Other error occurred:', error.message);
            }
          }
        } else {
          console.log('❌ Edit button not found');
        }
        
        // Clean up - remove modal
        modal.remove();
        console.log('🧹 Modal cleaned up');
        
      } else {
        console.log('❌ Modal element not found');
      }
      
    } catch (error) {
      console.log('❌ Error creating modal:', error.message);
    }
    
  } else {
    console.log('❌ CoderCompanionInjector not available');
  }
}

// Test 2: Test handleEditItem method
function testHandleEditItem() {
  console.log('\n🔧 Test 2: handleEditItem Method');
  
  if (typeof window.coderCompanionInjector !== 'undefined' && 
      typeof window.coderCompanionInjector.handleEditItem === 'function') {
    
    console.log('✅ handleEditItem method available');
    
    const mockItem = {
      id: 'test-project-456',
      name: 'Another Test Project',
      status: 'active'
    };
    
    try {
      // This should not throw a ReferenceError
      window.coderCompanionInjector.handleEditItem(mockItem, 'project');
      console.log('✅ handleEditItem executed without ReferenceError');
    } catch (error) {
      if (error instanceof ReferenceError) {
        console.log('❌ ReferenceError in handleEditItem:', error.message);
      } else {
        console.log('⚠️ Other error in handleEditItem:', error.message);
      }
    }
    
  } else {
    console.log('❌ handleEditItem method not available');
  }
}

// Test 3: Test event listener attachment
function testEventListenerAttachment() {
  console.log('\n🎯 Test 3: Event Listener Attachment');
  
  // Create a test modal structure
  const testModal = document.createElement('div');
  testModal.id = 'test-modal';
  testModal.innerHTML = `
    <div class="cc-item-details">
      <button type="button" class="cc-btn cc-btn-primary" id="edit-item-btn">Edit Project</button>
    </div>
  `;
  
  // Store test data
  const testItem = { id: 'test-123', name: 'Test Item' };
  testModal.setAttribute('data-item-type', 'project');
  testModal.setAttribute('data-item-id', testItem.id);
  testModal.setAttribute('data-item-data', JSON.stringify(testItem));
  
  document.body.appendChild(testModal);
  
  // Test manual event listener attachment
  const editButton = testModal.querySelector('#edit-item-btn');
  if (editButton) {
    console.log('✅ Edit button found in test modal');
    
    let clickHandled = false;
    editButton.addEventListener('click', () => {
      console.log('🔄 Test edit button clicked');
      
      // Retrieve data from modal attributes
      const itemType = testModal.getAttribute('data-item-type');
      const itemData = JSON.parse(testModal.getAttribute('data-item-data'));
      
      if (itemType && itemData) {
        console.log('✅ Data retrieved successfully from modal attributes');
        clickHandled = true;
        
        // Test calling handleEditItem with retrieved data
        if (typeof window.coderCompanionInjector !== 'undefined') {
          try {
            window.coderCompanionInjector.handleEditItem(itemData, itemType);
            console.log('✅ handleEditItem called successfully with retrieved data');
          } catch (error) {
            console.log('❌ Error calling handleEditItem:', error.message);
          }
        }
      } else {
        console.log('❌ Failed to retrieve data from modal attributes');
      }
    });
    
    // Simulate click
    editButton.click();
    
    if (clickHandled) {
      console.log('✅ Event listener attachment test passed');
    } else {
      console.log('❌ Event listener attachment test failed');
    }
  } else {
    console.log('❌ Edit button not found in test modal');
  }
  
  // Clean up
  testModal.remove();
  console.log('🧹 Test modal cleaned up');
}

// Run all tests
function runEditButtonFixTests() {
  console.log('🚀 Starting Edit Button Fix Test Suite...');
  
  try {
    testShowItemDetailsModal();
    testHandleEditItem();
    testEventListenerAttachment();
    
    console.log('\n🎉 Edit Button Fix Test Suite Complete!');
    console.log('\n💡 If all tests pass, the ReferenceError should be fixed.');
    console.log('Try clicking an Edit button in the actual extension to verify.');
  } catch (error) {
    console.error('❌ Edit Button Fix Test Suite Failed:', error);
  }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runEditButtonFixTests };
} else {
  // Make available globally for browser console
  window.testEditButtonFix = runEditButtonFixTests;
}

console.log('📋 Edit button fix test script loaded. Run testEditButtonFix() to start tests.');
