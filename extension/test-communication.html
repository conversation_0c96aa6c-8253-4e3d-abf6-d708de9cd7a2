<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Communication Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Coder Companion Communication Test</h1>

    <div class="test-section">
        <h2>Extension Communication Test</h2>
        <p>This page tests the communication between the popup and content script.</p>

        <button id="test-ping">Test PING</button>
        <button id="test-open-sidebar">Test OPEN_SIDEBAR</button>
        <button id="clear-log">Clear Log</button>

        <div id="log" class="log"></div>
    </div>

    <div class="test-section">
        <h2>Extension Status</h2>
        <p id="extension-status">Extension not detected</p>
        <button id="check-extension">Check Extension Status</button>
    </div>

    <script>
        const logElement = document.getElementById('log');
        const extensionStatus = document.getElementById('extension-status');

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            logElement.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            logElement.innerHTML = '';
        }

        // Check if extension is available
        function checkExtensionStatus() {
            log('Checking extension status...');

            if (typeof chrome !== 'undefined' && chrome.runtime) {
                try {
                    // Try to send a ping to the background script
                    chrome.runtime.sendMessage({ type: 'PING' }, (response) => {
                        if (chrome.runtime.lastError) {
                            log('Extension not available: ' + chrome.runtime.lastError.message, 'error');
                            extensionStatus.textContent = 'Extension not available';
                            extensionStatus.style.color = '#dc3545';
                            return;
                        }

                        if (response && response.pong) {
                            log('Extension is available!', 'success');
                            extensionStatus.textContent = 'Extension available';
                            extensionStatus.style.color = '#28a745';
                        } else {
                            log('Extension responded but with unexpected format: ' + JSON.stringify(response), 'error');
                            extensionStatus.textContent = 'Extension response format error';
                            extensionStatus.style.color = '#dc3545';
                        }
                    });
                } catch (error) {
                    log('Error communicating with extension: ' + error.message, 'error');
                    extensionStatus.textContent = 'Extension communication error';
                    extensionStatus.style.color = '#dc3545';
                }
            } else {
                log('Chrome runtime not available', 'error');
                extensionStatus.textContent = 'Chrome runtime not available';
                extensionStatus.style.color = '#dc3545';
            }
        }

        // Test PING message
        function testPing() {
            log('Sending PING message...');

            if (typeof chrome !== 'undefined' && chrome.runtime) {
                chrome.runtime.sendMessage({ type: 'PING' }, (response) => {
                    if (chrome.runtime.lastError) {
                        log('PING failed: ' + chrome.runtime.lastError.message, 'error');
                        return;
                    }

                    log('PING response received: ' + JSON.stringify(response), 'success');
                });
            } else {
                log('Chrome runtime not available for PING test', 'error');
            }
        }

        // Test OPEN_SIDEBAR message
        function testOpenSidebar() {
            log('Sending OPEN_SIDEBAR message...');

            if (typeof chrome !== 'undefined' && chrome.runtime) {
                // First get the active tab
                chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                    if (tabs.length > 0) {
                        const activeTab = tabs[0];
                        log('Active tab found: ' + activeTab.url);

                        // Send message to the active tab's content script
                        chrome.tabs.sendMessage(activeTab.id, { type: 'OPEN_SIDEBAR' }, (response) => {
                            if (chrome.runtime.lastError) {
                                log('OPEN_SIDEBAR failed: ' + chrome.runtime.lastError.message, 'error');
                                return;
                            }

                            if (response) {
                                log('OPEN_SIDEBAR response received: ' + JSON.stringify(response), 'success');
                            } else {
                                log('OPEN_SIDEBAR response was undefined', 'error');
                            }
                        });
                    } else {
                        log('No active tab found', 'error');
                    }
                });
            } else {
                log('Chrome runtime not available for OPEN_SIDEBAR test', 'error');
            }
        }

        // Event listeners
        document.getElementById('test-ping').addEventListener('click', testPing);
        document.getElementById('test-open-sidebar').addEventListener('click', testOpenSidebar);
        document.getElementById('clear-log').addEventListener('click', clearLog);
        document.getElementById('check-extension').addEventListener('click', checkExtensionStatus);

        // Initial check
        window.addEventListener('load', () => {
            setTimeout(checkExtensionStatus, 1000);
        });
    </script>
</body>
</html>