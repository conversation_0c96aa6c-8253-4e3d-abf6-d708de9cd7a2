// Test script for edit integration between content script and sidebar
// This script can be run in the browser console to test the integration

console.log('🧪 Testing Edit Integration...');

// Test 1: Check if content script has edit methods
function testContentScriptMethods() {
  console.log('\n📋 Test 1: Content Script Edit Methods');
  
  // Check if CoderCompanionInjector exists
  if (typeof window.coderCompanionInjector !== 'undefined') {
    console.log('✅ CoderCompanionInjector - OK');
    
    const requiredMethods = ['handleEditItem', 'openSidebarWithEdit'];
    requiredMethods.forEach(method => {
      if (typeof window.coderCompanionInjector[method] === 'function') {
        console.log(`✅ ${method} - OK`);
      } else {
        console.log(`❌ ${method} - MISSING`);
      }
    });
  } else {
    console.log('❌ CoderCompanionInjector - NOT AVAILABLE');
  }
}

// Test 2: Check if sidebar has edit handling methods
function testSidebarEditHandling() {
  console.log('\n🪟 Test 2: Sidebar Edit Handling');
  
  if (typeof window.sidebarApp !== 'undefined') {
    console.log('✅ window.sidebarApp - OK');
    
    const requiredMethods = ['handleEditFromContentScript'];
    requiredMethods.forEach(method => {
      if (typeof window.sidebarApp[method] === 'function') {
        console.log(`✅ ${method} - OK`);
      } else {
        console.log(`❌ ${method} - MISSING`);
      }
    });
  } else {
    console.log('❌ window.sidebarApp - NOT AVAILABLE');
  }
}

// Test 3: Test message passing
function testMessagePassing() {
  console.log('\n📨 Test 3: Message Passing');
  
  try {
    // Test chrome.runtime.sendMessage
    if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
      console.log('✅ chrome.runtime.sendMessage - OK');
      
      // Test sending a message
      chrome.runtime.sendMessage({
        type: 'SIDEBAR_ACTION',
        action: 'test',
        itemType: 'project',
        itemId: 'test-id'
      }, (response) => {
        if (chrome.runtime.lastError) {
          console.log('⚠️ Message sending - ERROR:', chrome.runtime.lastError.message);
        } else {
          console.log('✅ Message sending - OK');
        }
      });
    } else {
      console.log('❌ chrome.runtime.sendMessage - NOT AVAILABLE');
    }
  } catch (error) {
    console.log('❌ Message passing test failed:', error.message);
  }
}

// Test 4: Test postMessage communication
function testPostMessage() {
  console.log('\n📬 Test 4: PostMessage Communication');
  
  try {
    // Check if window.postMessage is available
    if (typeof window.postMessage === 'function') {
      console.log('✅ window.postMessage - OK');
      
      // Test posting a message
      window.postMessage({
        type: 'EDIT_ITEM',
        itemType: 'project',
        itemId: 'test-id'
      }, '*');
      
      console.log('✅ PostMessage test - OK');
    } else {
      console.log('❌ window.postMessage - NOT AVAILABLE');
    }
  } catch (error) {
    console.log('❌ PostMessage test failed:', error.message);
  }
}

// Test 5: Test sidebar frame detection
function testSidebarFrameDetection() {
  console.log('\n🖼️ Test 5: Sidebar Frame Detection');
  
  const sidebarFrame = document.querySelector('#coder-companion-sidebar iframe');
  if (sidebarFrame) {
    console.log('✅ Sidebar iframe - FOUND');
    
    if (sidebarFrame.contentWindow) {
      console.log('✅ Sidebar contentWindow - OK');
    } else {
      console.log('❌ Sidebar contentWindow - NOT ACCESSIBLE');
    }
  } else {
    console.log('❌ Sidebar iframe - NOT FOUND');
    
    // Check for sidebar container
    const sidebarContainer = document.querySelector('#coder-companion-sidebar');
    if (sidebarContainer) {
      console.log('✅ Sidebar container - FOUND');
    } else {
      console.log('❌ Sidebar container - NOT FOUND');
    }
  }
}

// Test 6: Simulate edit button click
function testEditButtonClick() {
  console.log('\n🖱️ Test 6: Edit Button Click Simulation');
  
  // Create a mock item
  const mockItem = {
    id: 'test-project-123',
    name: 'Test Project',
    description: 'A test project for edit functionality',
    status: 'active',
    createdAt: new Date().toISOString(),
    lastModified: new Date().toISOString()
  };
  
  try {
    if (typeof window.coderCompanionInjector !== 'undefined' && 
        typeof window.coderCompanionInjector.handleEditItem === 'function') {
      
      console.log('🔄 Simulating edit button click...');
      window.coderCompanionInjector.handleEditItem(mockItem, 'project');
      console.log('✅ Edit button click simulation - OK');
    } else {
      console.log('❌ Cannot simulate edit button click - handleEditItem not available');
    }
  } catch (error) {
    console.log('❌ Edit button click simulation failed:', error.message);
  }
}

// Test 7: Check for edit functionality alerts
function testForEditAlerts() {
  console.log('\n🚨 Test 7: Check for Edit Functionality Alerts');
  
  // Override alert to catch any remaining "edit functionality coming soon" alerts
  const originalAlert = window.alert;
  let alertCaught = false;
  
  window.alert = function(message) {
    if (message && message.toLowerCase().includes('edit functionality coming soon')) {
      console.log('❌ Found remaining edit alert:', message);
      alertCaught = true;
    }
    originalAlert.call(window, message);
  };
  
  // Restore original alert after a short delay
  setTimeout(() => {
    window.alert = originalAlert;
    if (!alertCaught) {
      console.log('✅ No edit functionality alerts found');
    }
  }, 1000);
}

// Run all tests
function runEditIntegrationTests() {
  console.log('🚀 Starting Edit Integration Test Suite...');
  
  try {
    testContentScriptMethods();
    testSidebarEditHandling();
    testMessagePassing();
    testPostMessage();
    testSidebarFrameDetection();
    testEditButtonClick();
    testForEditAlerts();
    
    console.log('\n🎉 Edit Integration Test Suite Complete!');
    console.log('Check the results above for any issues.');
    console.log('\n💡 To test manually:');
    console.log('1. Open the sidebar');
    console.log('2. Create a project/persona/artifact');
    console.log('3. Click on it to view details');
    console.log('4. Click "Edit" button');
    console.log('5. Verify the edit modal opens');
  } catch (error) {
    console.error('❌ Edit Integration Test Suite Failed:', error);
  }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runEditIntegrationTests };
} else {
  // Make available globally for browser console
  window.testEditIntegration = runEditIntegrationTests;
}

console.log('📋 Edit integration test script loaded. Run testEditIntegration() to start tests.');
