<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Extension Context</title>
  <link rel="stylesheet" href="sidebar/style.css">
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f5f5f5;
    }
    .test-container {
      max-width: 400px;
      margin: 0 auto;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      overflow: hidden;
    }
    .test-header {
      background: #059669;
      color: white;
      padding: 15px;
      text-align: center;
    }
    .test-content {
      height: 600px;
      overflow: hidden;
    }
    .debug-info {
      padding: 15px;
      background: #ecfdf5;
      border-bottom: 1px solid #a7f3d0;
      font-size: 12px;
    }
    .debug-log {
      position: fixed;
      bottom: 20px;
      right: 20px;
      width: 350px;
      max-height: 300px;
      background: #1f2937;
      color: #f9fafb;
      padding: 10px;
      border-radius: 8px;
      font-family: monospace;
      font-size: 11px;
      overflow-y: auto;
      z-index: 10000;
    }
    .debug-controls {
      padding: 10px;
      background: #f0fdf4;
      border-bottom: 1px solid #bbf7d0;
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
    }
    .debug-btn {
      padding: 4px 8px;
      background: #059669;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 11px;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <div class="test-header">
      <h2>🔧 Extension Context Test</h2>
      <p>Simulating exact extension environment</p>
    </div>
    <div class="debug-info">
      <strong>Context:</strong> Extension Simulation<br>
      <strong>Storage:</strong> <span id="storage-status">Checking...</span><br>
      <strong>Panels:</strong> <span id="panels-status">Loading...</span><br>
      <strong>Data:</strong> <span id="data-status">Checking...</span>
    </div>
    <div class="debug-controls">
      <button class="debug-btn" onclick="testStorage()">Test Storage</button>
      <button class="debug-btn" onclick="testPanels()">Test Panels</button>
      <button class="debug-btn" onclick="createTestProject()">Create Test Project</button>
      <button class="debug-btn" onclick="clearStorage()">Clear Storage</button>
      <button class="debug-btn" onclick="showStorageData()">Show Data</button>
    </div>
    <div class="test-content">
      <!-- This will be populated by content-script simulation -->
      <div id="sidebar-container-placeholder">
        <p style="padding: 20px; text-align: center; color: #6b7280;">
          Waiting for sidebar injection...
        </p>
      </div>
    </div>
  </div>

  <div id="debug-log" class="debug-log">
    <div><strong>Debug Log:</strong></div>
  </div>

  <script>
    // Simulate extension environment exactly
    console.log('[Test] Starting extension context simulation...');

    // Debug logging
    const debugLog = document.getElementById('debug-log');
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;

    function addToLog(level, ...args) {
      originalConsoleLog.apply(console, args);
      const logEntry = document.createElement('div');
      logEntry.style.color = level === 'error' ? '#ef4444' : level === 'warn' ? '#f59e0b' : '#f9fafb';
      logEntry.textContent = `[${level.toUpperCase()}] ${args.join(' ')}`;
      debugLog.appendChild(logEntry);
      debugLog.scrollTop = debugLog.scrollHeight;
    }

    console.log = (...args) => addToLog('log', ...args);
    console.error = (...args) => addToLog('error', ...args);
    console.warn = (...args) => addToLog('warn', ...args);

    // Simulate content-script injection
    function simulateContentScriptInjection() {
      console.log('[Test] Simulating content-script injection...');
      
      // Create sidebar container like content-script does
      const sidebarContainer = document.createElement('div');
      sidebarContainer.id = 'cc-sidebar';
      
      // Load sidebar HTML (simulate fetch)
      const sidebarHTML = `
        <div id="sidebar-container" class="sidebar-container">
          <header class="sidebar-header">
            <h2>Coder Companion</h2>
          </header>
          <nav class="sidebar-tabs">
            <button class="tab-button active" data-tab="projects">Projects</button>
            <button class="tab-button" data-tab="personas">Personas</button>
            <button class="tab-button" data-tab="artifacts">Artifacts</button>
            <button class="tab-button" data-tab="export">Export</button>
          </nav>
          <main class="sidebar-content">
            <div id="projects-tab" class="tab-content active"></div>
            <div id="personas-tab" class="tab-content"></div>
            <div id="artifacts-tab" class="tab-content"></div>
            <div id="export-tab" class="tab-content"></div>
          </main>
        </div>
      `;
      
      sidebarContainer.innerHTML = sidebarHTML;
      
      // Replace placeholder
      const placeholder = document.getElementById('sidebar-container-placeholder');
      placeholder.parentNode.replaceChild(sidebarContainer, placeholder);
      
      console.log('[Test] Sidebar HTML injected');
      
      // Now load the script
      const script = document.createElement('script');
      script.src = 'sidebar/script.js';
      script.onload = () => {
        console.log('[Test] Sidebar script loaded');
        updateStatus();
      };
      script.onerror = (error) => {
        console.error('[Test] Failed to load sidebar script:', error);
      };
      document.head.appendChild(script);
    }

    // Test functions
    function testStorage() {
      console.log('[Test] Testing storage...');
      if (window.sidebarManager && window.sidebarManager.storage) {
        window.sidebarManager.storage.set('test', { message: 'Hello World', timestamp: Date.now() });
        console.log('[Test] Storage test completed');
      } else {
        console.error('[Test] Storage not available');
      }
    }

    function testPanels() {
      console.log('[Test] Testing panels...');
      const panels = ['projects-tab', 'personas-tab', 'artifacts-tab', 'export-tab'];
      panels.forEach(panelId => {
        const panel = document.getElementById(panelId);
        console.log(`[Test] Panel ${panelId}:`, panel ? 'Found' : 'Not found', panel?.innerHTML?.length || 0, 'chars');
      });
    }

    async function createTestProject() {
      console.log('[Test] Creating test project...');
      if (window.sidebarManager && window.sidebarManager.projectManager) {
        try {
          const project = await window.sidebarManager.projectManager.createProject({
            name: 'Test Project',
            description: 'Created from test page',
            priority: 'high',
            tags: ['test', 'debug']
          });
          console.log('[Test] Test project created:', project);
          await window.sidebarManager.loadTabData('projects');
        } catch (error) {
          console.error('[Test] Failed to create test project:', error);
        }
      } else {
        console.error('[Test] Project manager not available');
      }
    }

    function clearStorage() {
      console.log('[Test] Clearing storage...');
      localStorage.clear();
      console.log('[Test] Storage cleared');
    }

    async function showStorageData() {
      console.log('[Test] Storage data:');
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        const value = localStorage.getItem(key);
        console.log(`[Test] ${key}:`, value);
      }
    }

    function updateStatus() {
      document.getElementById('storage-status').textContent = 
        window.sidebarManager?.storage ? 'Available ✅' : 'Not Available ❌';
      document.getElementById('panels-status').textContent = 
        window.sidebarManager?.panelsLoaded ? 'Loaded ✅' : 'Not Loaded ❌';
      document.getElementById('data-status').textContent = 
        window.sidebarManager?.projectManager ? 'Ready ✅' : 'Not Ready ❌';
    }

    // Start simulation
    setTimeout(simulateContentScriptInjection, 100);
    
    // Update status periodically
    setInterval(updateStatus, 1000);
  </script>
</body>
</html>
