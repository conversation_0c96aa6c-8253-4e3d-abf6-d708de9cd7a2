// background.js - Service worker for Coder Companion extension

class CoderCompanionBackground {
  constructor() {
    this.connectedPorts = new Map();
    this.extensionState = {
      isEnabled: true,
      lastActiveTime: null,
      version: chrome.runtime.getManifest().version
    };

    this.init();
  }

  init() {
    console.log('[Coder Companion BG] Initializing background service worker...');

    // Handle extension installation and updates
    chrome.runtime.onInstalled.addListener(this.handleInstalled.bind(this));

    // Handle messages from popup, content scripts, and sidebar
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));

    // Handle connections from different contexts
    chrome.runtime.onConnect.addListener(this.handleConnect.bind(this));

    // Handle extension startup
    this.handleStartup();
  }

  handleInstalled(details) {
    console.log('[Coder Companion BG] Extension installed/updated:', details.reason);

    if (details.reason === 'install') {
      // First time installation
      this.initializeDefaultSettings();
    } else if (details.reason === 'update') {
      // Handle updates and migrations
      this.handleUpdate(details.previousVersion);
    }

    // Initialize storage with default data structure
    this.initializeStorage();
  }

  handleStartup() {
    console.log('[Coder Companion BG] Extension startup');

    // Load saved state
    chrome.storage.local.get(['extensionState'], (result) => {
      if (result.extensionState) {
        this.extensionState = { ...this.extensionState, ...result.extensionState };
      }
      this.extensionState.lastActiveTime = Date.now();
      this.saveState();
    });
  }

  handleMessage(message, sender, sendResponse) {
    console.log('[Coder Companion BG] Received message:', message);

    switch (message.type) {
      case 'GET_EXTENSION_STATE':
        sendResponse(this.extensionState);
        break;

      case 'UPDATE_EXTENSION_STATE':
        this.extensionState = { ...this.extensionState, ...message.data };
        this.extensionState.lastActiveTime = Date.now();
        this.saveState();
        sendResponse({ success: true });
        break;

      case 'PING':
        sendResponse({ pong: true, timestamp: Date.now() });
        break;

      case 'GET_ACTIVE_TAB_INFO':
        this.getActiveTabInfo(sendResponse);
        return true; // Keep message channel open for async response

      default:
        console.warn('[Coder Companion BG] Unknown message type:', message.type);
        sendResponse({ error: 'Unknown message type' });
    }
  }

  handleConnect(port) {
    console.log('[Coder Companion BG] New connection from:', port.name);

    this.connectedPorts.set(port.name, port);

    port.onMessage.addListener((message) => {
      this.handlePortMessage(port, message);
    });

    port.onDisconnect.addListener(() => {
      console.log('[Coder Companion BG] Port disconnected:', port.name);
      this.connectedPorts.delete(port.name);
    });
  }

  handlePortMessage(port, message) {
    console.log(`[Coder Companion BG] Port message from ${port.name}:`, message);

    // Handle port-specific messages
    switch (message.type) {
      case 'CONTENT_SCRIPT_READY':
        this.handleContentScriptReady(port);
        break;

      case 'SIDEBAR_ACTION':
        this.broadcastToAllPorts(message, port.name);
        break;

      default:
        // Forward message to other ports if needed
        this.forwardMessageToPorts(message, port.name);
    }
  }

  handleContentScriptReady(port) {
    // Content script is ready, can send initialization data
    port.postMessage({
      type: 'BACKGROUND_INIT',
      data: {
        extensionState: this.extensionState,
        timestamp: Date.now()
      }
    });
  }

  broadcastToAllPorts(message, excludePort = null) {
    this.connectedPorts.forEach((port, name) => {
      if (name !== excludePort) {
        try {
          port.postMessage(message);
        } catch (error) {
          console.error(`[Coder Companion BG] Error broadcasting to ${name}:`, error);
        }
      }
    });
  }

  forwardMessageToPorts(message, fromPort) {
    // Forward messages between different contexts
    this.connectedPorts.forEach((port, name) => {
      if (name !== fromPort) {
        try {
          port.postMessage({
            ...message,
            forwarded: true,
            from: fromPort
          });
        } catch (error) {
          console.error(`[Coder Companion BG] Error forwarding to ${name}:`, error);
        }
      }
    });
  }

  async getActiveTabInfo(sendResponse) {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length > 0) {
        const tab = tabs[0];
        sendResponse({
          tabId: tab.id,
          url: tab.url,
          title: tab.title
        });
      } else {
        sendResponse({ error: 'No active tab found' });
      }
    } catch (error) {
      console.error('[Coder Companion BG] Error getting active tab info:', error);
      sendResponse({ error: error.message });
    }
  }

  initializeDefaultSettings() {
    const defaultSettings = {
      extensionState: this.extensionState,
      userPreferences: {
        theme: 'light',
        sidebarWidth: 384, // 24rem
        autoSave: true,
        autoSaveInterval: 2000
      },
      dataSchema: {
        version: '1.0',
        projects: [],
        personas: [],
        artifacts: []
      }
    };

    chrome.storage.local.set(defaultSettings);
    console.log('[Coder Companion BG] Default settings initialized');
  }

  initializeStorage() {
    // Ensure data structure exists
    chrome.storage.local.get(['dataSchema'], (result) => {
      if (!result.dataSchema) {
        this.initializeDefaultSettings();
      }
    });
  }

  handleUpdate(previousVersion) {
    console.log(`[Coder Companion BG] Updating from ${previousVersion} to ${this.extensionState.version}`);

    // Handle data migrations if needed
    // This will be expanded as the extension evolves
  }

  saveState() {
    chrome.storage.local.set({
      extensionState: this.extensionState
    });
  }

  // Cleanup method for periodic maintenance
  cleanup() {
    // Remove disconnected ports
    const connectedPortNames = Array.from(this.connectedPorts.keys());
    chrome.tabs.query({}, (tabs) => {
      const activeTabIds = tabs.map(tab => tab.id);

      // This is a simplified cleanup - in practice you'd need more sophisticated logic
      console.log('[Coder Companion BG] Running cleanup');
    });
  }
}

// Initialize the background service
const backgroundService = new CoderCompanionBackground();

// Periodic cleanup (every 5 minutes)
setInterval(() => {
  backgroundService.cleanup();
}, 5 * 60 * 1000);

console.log('[Coder Companion BG] Background service worker initialized');