<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Coder Companion - Test Runner</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: #f8fafc;
      color: #1f2937;
      line-height: 1.6;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      text-align: center;
      margin-bottom: 30px;
      padding: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .header h1 {
      color: #1e40af;
      margin-bottom: 10px;
    }

    .test-controls {
      display: flex;
      gap: 10px;
      justify-content: center;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }

    .btn {
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.2s ease;
    }

    .btn-primary {
      background: #3b82f6;
      color: white;
    }

    .btn-primary:hover {
      background: #2563eb;
    }

    .btn-secondary {
      background: #6b7280;
      color: white;
    }

    .btn-secondary:hover {
      background: #4b5563;
    }

    .btn-success {
      background: #10b981;
      color: white;
    }

    .btn-danger {
      background: #ef4444;
      color: white;
    }

    .test-results {
      background: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      padding: 20px;
    }

    .test-section {
      margin-bottom: 30px;
      border-bottom: 1px solid #e5e7eb;
      padding-bottom: 20px;
    }

    .test-section:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    .test-section h2 {
      color: #1e40af;
      margin-bottom: 15px;
      padding-bottom: 5px;
      border-bottom: 2px solid #3b82f6;
    }

    .test-item {
      display: flex;
      align-items: center;
      gap: 10px;
      padding: 10px;
      margin-bottom: 5px;
      border-radius: 4px;
      background: #f8fafc;
    }

    .test-status {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
    }

    .test-status.pending { background: #d1d5db; }
    .test-status.running { background: #3b82f6; }
    .test-status.passed { background: #10b981; color: white; }
    .test-status.failed { background: #ef4444; color: white; }

    .test-name {
      flex: 1;
      font-weight: 500;
    }

    .test-time {
      font-size: 12px;
      color: #6b7280;
    }

    .test-error {
      margin-top: 5px;
      padding: 5px 10px;
      background: #fef2f2;
      border-left: 3px solid #ef4444;
      color: #991b1b;
      font-size: 12px;
      font-family: monospace;
    }

    .summary {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }

    .summary-item {
      text-align: center;
      padding: 15px;
      border-radius: 8px;
      background: white;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .summary-number {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .summary-label {
      font-size: 14px;
      color: #6b7280;
    }

    .summary-passed .summary-number { color: #10b981; }
    .summary-failed .summary-number { color: #ef4444; }
    .summary-pending .summary-number { color: #6b7280; }
    .summary-running .summary-number { color: #3b82f6; }

    .progress-bar {
      width: 100%;
      height: 4px;
      background: #e5e7eb;
      border-radius: 2px;
      overflow: hidden;
      margin-bottom: 20px;
    }

    .progress-fill {
      height: 100%;
      background: #3b82f6;
      width: 0%;
      transition: width 0.3s ease;
    }

    .log-section {
      margin-top: 30px;
      padding: 15px;
      background: #1f2937;
      color: #f9fafb;
      border-radius: 8px;
      font-family: monospace;
      font-size: 12px;
      max-height: 200px;
      overflow-y: auto;
    }

    .log-entry {
      margin-bottom: 5px;
      padding: 2px 0;
    }

    .log-info { color: #3b82f6; }
    .log-success { color: #10b981; }
    .log-error { color: #ef4444; }
    .log-warning { color: #f59e0b; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Coder Companion - Test Runner</h1>
      <p>Comprehensive testing suite for all extension functionality</p>
    </div>

    <div class="test-controls">
      <button id="run-all-tests" class="btn btn-primary">Run All Tests</button>
      <button id="run-core-tests" class="btn btn-secondary">Core Tests Only</button>
      <button id="run-ui-tests" class="btn btn-secondary">UI Tests Only</button>
      <button id="clear-results" class="btn btn-danger">Clear Results</button>
    </div>

    <div class="progress-bar">
      <div class="progress-fill" id="progress-fill"></div>
    </div>

    <div class="summary">
      <div class="summary-item summary-passed">
        <div class="summary-number" id="passed-count">0</div>
        <div class="summary-label">Passed</div>
      </div>
      <div class="summary-item summary-failed">
        <div class="summary-number" id="failed-count">0</div>
        <div class="summary-label">Failed</div>
      </div>
      <div class="summary-item summary-pending">
        <div class="summary-number" id="pending-count">0</div>
        <div class="summary-label">Pending</div>
      </div>
      <div class="summary-item summary-running">
        <div class="summary-number" id="running-count">0</div>
        <div class="summary-label">Running</div>
      </div>
    </div>

    <div class="test-results">
      <div class="test-section">
        <h2>Core Functionality Tests</h2>
        <div id="core-tests"></div>
      </div>

      <div class="test-section">
        <h2>UI Component Tests</h2>
        <div id="ui-tests"></div>
      </div>

      <div class="test-section">
        <h2>Integration Tests</h2>
        <div id="integration-tests"></div>
      </div>

      <div class="test-section">
        <h2>Performance Tests</h2>
        <div id="performance-tests"></div>
      </div>
    </div>

    <div class="log-section">
      <div id="test-log"></div>
    </div>
  </div>

  <script src="test-framework.js"></script>
  <script src="core-tests.js"></script>
  <script src="ui-tests.js"></script>
  <script src="integration-tests.js"></script>
  <script src="performance-tests.js"></script>
  <script>
    // Initialize test runner
    document.addEventListener('DOMContentLoaded', () => {
      const testRunner = new TestRunner();

      // Setup event listeners
      document.getElementById('run-all-tests').addEventListener('click', () => {
        testRunner.runAllTests();
      });

      document.getElementById('run-core-tests').addEventListener('click', () => {
        testRunner.runCoreTests();
      });

      document.getElementById('run-ui-tests').addEventListener('click', () => {
        testRunner.runUITests();
      });

      document.getElementById('clear-results').addEventListener('click', () => {
        testRunner.clearResults();
      });
    });
  </script>
</body>
</html>