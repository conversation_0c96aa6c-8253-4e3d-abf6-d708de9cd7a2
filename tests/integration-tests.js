// tests/integration-tests.js - Integration tests for cross-module interactions

// Test module integration
function testModuleIntegration() {
  testRunner.registerTest('integration', 'Module Loading', () => {
    // Test that modules can be loaded without errors
    assert.truthy(typeof MockStorage !== 'undefined');
    assert.truthy(typeof assert !== 'undefined');
    assert.truthy(typeof TestRunner !== 'undefined');
  });

  testRunner.registerTest('integration', 'Cross-Module Data Flow', async () => {
    const mockStorage = new MockStorage();

    // Simulate data flow between modules
    const testProject = {
      id: 'test-project',
      name: 'Integration Test Project',
      status: 'active'
    };

    const testPersona = {
      id: 'test-persona',
      name: 'Integration Test Persona',
      status: 'active'
    };

    const testArtifact = {
      id: 'test-artifact',
      name: 'Integration Test Artifact',
      projectId: 'test-project',
      personaId: 'test-persona',
      content: 'Test content'
    };

    // Store test data
    await mockStorage.set('projects', [testProject]);
    await mockStorage.set('personas', [testPersona]);
    await mockStorage.set('artifacts', [testArtifact]);

    // Verify data integrity
    const projects = await mockStorage.get('projects', []);
    const personas = await mockStorage.get('personas', []);
    const artifacts = await mockStorage.get('artifacts', []);

    assert.equal(projects.length, 1);
    assert.equal(personas.length, 1);
    assert.equal(artifacts.length, 1);

    // Verify relationships
    const artifact = artifacts[0];
    assert.equal(artifact.projectId, testProject.id);
    assert.equal(artifact.personaId, testPersona.id);
  });
}

// Test extension lifecycle
function testExtensionLifecycle() {
  testRunner.registerTest('integration', 'Extension Initialization', () => {
    // Test that basic extension components can be initialized
    const testElement = document.createElement('div');
    testElement.innerHTML = '<div id="test-root">Test Content</div>';

    const root = testElement.querySelector('#test-root');
    assert.truthy(root);
    assert.equal(root.textContent, 'Test Content');
  });

  testRunner.registerTest('integration', 'Event System', () => {
    return new Promise((resolve) => {
      const testElement = document.createElement('button');
      let eventFired = false;

      testElement.addEventListener('click', () => {
        eventFired = true;
      });

      // Simulate user interaction
      testElement.click();

      setTimeout(() => {
        resolve(eventFired);
      }, 50);
    });
  });
}

// Test data persistence
function testDataPersistence() {
  testRunner.registerTest('integration', 'Data Persistence', async () => {
    const mockStorage = new MockStorage();
    const testData = {
      id: 'persistence-test',
      name: 'Persistence Test',
      timestamp: Date.now()
    };

    // Store data
    await mockStorage.set('persistence-test', testData);

    // Retrieve data
    const retrieved = await mockStorage.get('persistence-test');

    assert.equal(retrieved.id, testData.id);
    assert.equal(retrieved.name, testData.name);
    assert.equal(retrieved.timestamp, testData.timestamp);
  });

  testRunner.registerTest('integration', 'Data Migration', async () => {
    const mockStorage = new MockStorage();

    // Test basic data migration scenario
    const oldData = {
      projects: [
        { id: '1', title: 'Old Project' } // Old format
      ]
    };

    await mockStorage.set('legacy-data', oldData);

    // Simulate migration
    const migratedData = {
      projects: [
        { id: '1', name: 'Old Project', title: 'Old Project' } // New format
      ]
    };

    await mockStorage.set('legacy-data', migratedData);

    const result = await mockStorage.get('legacy-data');
    assert.equal(result.projects[0].name, 'Old Project');
    assert.equal(result.projects[0].title, 'Old Project');
  });
}

// Test export functionality
function testExportIntegration() {
  testRunner.registerTest('integration', 'Export Pipeline', async () => {
    const mockStorage = new MockStorage();

    // Setup test data
    const testArtifact = {
      id: 'export-test',
      name: 'Export Test Artifact',
      content: '# Export Test\n\nThis is test content.',
      projectId: 'test-project',
      personaId: 'test-persona'
    };

    await mockStorage.set('artifacts', [testArtifact]);

    // Simulate export process
    const exportData = {
      fileName: 'export-test-artifact.md',
      content: '# Export Test\n\nThis is test content.',
      contentType: 'text/markdown',
      size: 42
    };

    assert.equal(exportData.fileName.includes('export-test'), true);
    assert.equal(exportData.contentType, 'text/markdown');
    assert.equal(typeof exportData.size, 'number');
  });

  testRunner.registerTest('integration', 'Bulk Export Process', async () => {
    const mockStorage = new MockStorage();

    // Setup multiple projects
    const projects = [
      { id: 'project-1', name: 'Project 1' },
      { id: 'project-2', name: 'Project 2' }
    ];

    await mockStorage.set('projects', projects);

    // Simulate bulk export
    const exportResult = {
      fileName: 'all-projects-2024-01-01.zip',
      contentType: 'application/zip',
      projectCount: projects.length,
      success: true
    };

    assert.equal(exportResult.contentType, 'application/zip');
    assert.equal(exportResult.projectCount, 2);
    assert.equal(exportResult.success, true);
  });
}

// Test error handling
function testErrorHandling() {
  testRunner.registerTest('integration', 'Error Recovery', () => {
    try {
      // Simulate an error condition
      throw new Error('Simulated error');
    } catch (error) {
      assert.equal(error.message, 'Simulated error');
      assert.truthy(error instanceof Error);
    }
  });

  testRunner.registerTest('integration', 'Graceful Degradation', () => {
    // Test that system can handle missing dependencies
    const missingFeature = typeof window.nonExistentFeature;
    assert.equal(missingFeature, 'undefined');

    // System should continue to work despite missing feature
    const basicFunctionality = 1 + 1;
    assert.equal(basicFunctionality, 2);
  });
}

// Test browser compatibility
function testBrowserCompatibility() {
  testRunner.registerTest('integration', 'Modern API Support', () => {
    // Test for modern browser APIs
    const hasLocalStorage = typeof Storage !== 'undefined';
    const hasFetch = typeof fetch !== 'undefined';
    const hasPromise = typeof Promise !== 'undefined';

    assert.equal(hasLocalStorage, true);
    assert.equal(hasFetch, true);
    assert.equal(hasPromise, true);
  });

  testRunner.registerTest('integration', 'CSS Feature Support', () => {
    const testElement = document.createElement('div');

    // Test for CSS Grid support
    testElement.style.display = 'grid';
    const hasGrid = getComputedStyle(testElement).display === 'grid';

    // Test for Flexbox support
    testElement.style.display = 'flex';
    const hasFlex = getComputedStyle(testElement).display === 'flex';

    // These should be supported in modern browsers
    assert.equal(hasGrid, true);
    assert.equal(hasFlex, true);
  });

  testRunner.registerTest('integration', 'ES6+ Feature Support', () => {
    // Test for ES6+ features
    const hasArrowFunctions = typeof (() => {}) === 'function';
    const hasTemplateLiterals = typeof `${1}` === 'string';
    const hasPromises = typeof Promise !== 'undefined';
    const hasAsyncAwait = true; // If this runs, async/await is supported

    assert.equal(hasArrowFunctions, true);
    assert.equal(hasTemplateLiterals, true);
    assert.equal(hasPromises, true);
    assert.equal(hasAsyncAwait, true);
  });
}

// Test performance scenarios
function testPerformanceScenarios() {
  testRunner.registerTest('integration', 'Memory Usage', () => {
    const startMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;

    // Create some objects to test memory usage
    const testObjects = [];
    for (let i = 0; i < 1000; i++) {
      testObjects.push({
        id: `test-${i}`,
        name: `Test Object ${i}`,
        data: 'x'.repeat(1000) // 1KB of data
      });
    }

    const endMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;

    if (performance.memory) {
      const memoryIncrease = endMemory - startMemory;
      console.log(`Memory increase: ${memoryIncrease} bytes`);
      assert.equal(memoryIncrease > 0, true);
    } else {
      // If memory API not available, just pass the test
      assert.equal(true, true);
    }

    // Clean up
    testObjects.length = 0;
  });

  testRunner.registerTest('integration', 'Large Dataset Handling', async () => {
    const mockStorage = new MockStorage();

    // Create a large dataset
    const largeDataset = [];
    for (let i = 0; i < 100; i++) {
      largeDataset.push({
        id: `item-${i}`,
        name: `Test Item ${i}`,
        description: 'x'.repeat(100), // 100 chars
        data: 'y'.repeat(1000) // 1KB of additional data
      });
    }

    const startTime = Date.now();

    await mockStorage.set('large-dataset', largeDataset);
    const retrieved = await mockStorage.get('large-dataset', []);

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`Large dataset operation took ${duration}ms`);
    assert.equal(retrieved.length, 100);
    assert.equal(duration < 5000, true); // Should complete within 5 seconds
  });
}

// Test security scenarios
function testSecurityScenarios() {
  testRunner.registerTest('integration', 'XSS Prevention', () => {
    const maliciousInput = '<script>alert("XSS")</script>';
    const sanitizedInput = maliciousInput.replace(/</g, '<').replace(/>/g, '>');

    assert.equal(sanitizedInput.includes('<script>'), false);
    assert.equal(sanitizedInput.includes('<script>'), true);
  });

  testRunner.registerTest('integration', 'Data Validation', () => {
    const testCases = [
      { input: { id: 'test', name: 'Valid Name' }, expected: true },
      { input: { id: '', name: '' }, expected: false },
      { input: { id: 'test', name: 'a'.repeat(101) }, expected: false }, // Too long
      { input: { id: 'test', name: 'Valid Name', unexpectedField: 'value' }, expected: false }
    ];

    testCases.forEach(testCase => {
      const isValid = testCase.input.id && testCase.input.name &&
                     testCase.input.name.length <= 100 &&
                     !testCase.input.unexpectedField;

      assert.equal(isValid, testCase.expected);
    });
  });
}

// Initialize all integration tests
document.addEventListener('DOMContentLoaded', () => {
  testModuleIntegration();
  testExtensionLifecycle();
  testDataPersistence();
  testExportIntegration();
  testErrorHandling();
  testBrowserCompatibility();
  testPerformanceScenarios();
  testSecurityScenarios();

  console.log('Integration tests initialized');
});