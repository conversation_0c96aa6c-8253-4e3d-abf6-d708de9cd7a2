// tests/test-framework.js - Test framework for Coder Companion

class TestRunner {
  constructor() {
    this.tests = {
      core: [],
      ui: [],
      integration: [],
      performance: []
    };
    this.results = {
      passed: 0,
      failed: 0,
      pending: 0,
      running: 0
    };
    this.currentTest = null;
    this.testLog = [];

    this.initialize();
  }

  initialize() {
    this.log('Test framework initialized');
  }

  registerTest(category, name, testFunction) {
    if (!this.tests[category]) {
      this.tests[category] = [];
    }

    this.tests[category].push({
      name,
      function: testFunction,
      status: 'pending',
      error: null,
      duration: 0
    });

    this.results.pending++;
    this.updateUI();
  }

  async runTest(test) {
    test.status = 'running';
    test.error = null;
    this.currentTest = test;
    this.results.running++;
    this.results.pending--;
    this.updateUI();

    const startTime = Date.now();

    try {
      this.log(`Running test: ${test.name}`, 'info');
      const result = await test.function();

      if (result === false) {
        throw new Error('Test returned false');
      }

      test.status = 'passed';
      test.duration = Date.now() - startTime;
      this.results.passed++;
      this.results.running--;
      this.log(`✓ ${test.name} passed (${test.duration}ms)`, 'success');

    } catch (error) {
      test.status = 'failed';
      test.error = error.message;
      test.duration = Date.now() - startTime;
      this.results.failed++;
      this.results.running--;
      this.log(`✗ ${test.name} failed: ${error.message}`, 'error');
    }

    this.updateUI();
  }

  async runTests(category) {
    if (!this.tests[category]) {
      this.log(`Unknown test category: ${category}`, 'error');
      return;
    }

    this.log(`Running ${category} tests...`, 'info');

    for (const test of this.tests[category]) {
      await this.runTest(test);

      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    this.log(`${category} tests completed`, 'info');
  }

  async runAllTests() {
    this.log('Starting all tests...', 'info');

    for (const category of Object.keys(this.tests)) {
      await this.runTests(category);
    }

    this.log('All tests completed', 'info');
    this.showSummary();
  }

  async runCoreTests() {
    await this.runTests('core');
  }

  async runUITests() {
    await this.runTests('ui');
  }

  clearResults() {
    // Reset all test statuses
    for (const category of Object.keys(this.tests)) {
      this.tests[category].forEach(test => {
        test.status = 'pending';
        test.error = null;
        test.duration = 0;
      });
    }

    // Reset results
    this.results = {
      passed: 0,
      failed: 0,
      pending: Object.values(this.tests).flat().length,
      running: 0
    };

    this.currentTest = null;
    this.testLog = [];
    this.updateUI();
    this.log('Test results cleared', 'info');
  }

  updateUI() {
    // Update counters
    document.getElementById('passed-count').textContent = this.results.passed;
    document.getElementById('failed-count').textContent = this.results.failed;
    document.getElementById('pending-count').textContent = this.results.pending;
    document.getElementById('running-count').textContent = this.results.running;

    // Update progress bar
    const total = this.results.passed + this.results.failed + this.results.pending + this.results.running;
    const completed = this.results.passed + this.results.failed;
    const progress = total > 0 ? (completed / total) * 100 : 0;
    document.getElementById('progress-fill').style.width = `${progress}%`;

    // Update test results
    this.updateTestResults();
    this.updateLog();
  }

  updateTestResults() {
    const categories = ['core', 'ui', 'integration', 'performance'];

    categories.forEach(category => {
      const container = document.getElementById(`${category}-tests`);
      if (!container) return;

      const tests = this.tests[category] || [];
      container.innerHTML = tests.map(test => `
        <div class="test-item">
          <div class="test-status ${test.status}">
            ${test.status === 'running' ? '●' :
              test.status === 'passed' ? '✓' :
              test.status === 'failed' ? '✗' : '○'}
          </div>
          <div class="test-name">${test.name}</div>
          <div class="test-time">
            ${test.duration > 0 ? `${test.duration}ms` : ''}
          </div>
          ${test.error ? `<div class="test-error">${test.error}</div>` : ''}
        </div>
      `).join('');
    });
  }

  updateLog() {
    const logContainer = document.getElementById('test-log');
    if (!logContainer) return;

    logContainer.innerHTML = this.testLog.slice(-50).map(entry => `
      <div class="log-entry log-${entry.type}">[${new Date().toLocaleTimeString()}] ${entry.message}</div>
    `).join('');

    logContainer.scrollTop = logContainer.scrollHeight;
  }

  log(message, type = 'info') {
    this.testLog.push({
      message,
      type,
      timestamp: Date.now()
    });

    console.log(`[TestRunner] ${message}`);
    this.updateLog();
  }

  showSummary() {
    const total = this.results.passed + this.results.failed;
    const successRate = total > 0 ? (this.results.passed / total * 100).toFixed(1) : 0;

    this.log(`Test Summary: ${this.results.passed}/${total} passed (${successRate}%)`, 'info');

    if (this.results.failed > 0) {
      this.log(`${this.results.failed} tests failed`, 'error');
    } else {
      this.log('All tests passed! 🎉', 'success');
    }
  }
}

// Assertion helpers
const assert = {
  equal: (actual, expected, message) => {
    if (actual !== expected) {
      throw new Error(message || `Expected ${expected}, but got ${actual}`);
    }
  },

  notEqual: (actual, expected, message) => {
    if (actual === expected) {
      throw new Error(message || `Expected ${actual} to not equal ${expected}`);
    }
  },

  truthy: (value, message) => {
    if (!value) {
      throw new Error(message || `Expected ${value} to be truthy`);
    }
  },

  falsy: (value, message) => {
    if (value) {
      throw new Error(message || `Expected ${value} to be falsy`);
    }
  },

  throws: (fn, message) => {
    try {
      fn();
      throw new Error(message || 'Expected function to throw');
    } catch (error) {
      if (error.message === message) {
        throw error;
      }
    }
  },

  arrayEqual: (actual, expected, message) => {
    if (!Array.isArray(actual) || !Array.isArray(expected)) {
      throw new Error('Both arguments must be arrays');
    }

    if (actual.length !== expected.length) {
      throw new Error(message || `Array lengths differ: ${actual.length} vs ${expected.length}`);
    }

    for (let i = 0; i < actual.length; i++) {
      if (actual[i] !== expected[i]) {
        throw new Error(message || `Arrays differ at index ${i}: ${actual[i]} vs ${expected[i]}`);
      }
    }
  }
};

// Mock storage for testing
class MockStorage {
  constructor() {
    this.data = {};
  }

  async get(key, defaultValue = null) {
    return this.data[key] !== undefined ? this.data[key] : defaultValue;
  }

  async set(key, value) {
    this.data[key] = value;
    return true;
  }

  async remove(key) {
    delete this.data[key];
    return true;
  }
}

// Make globals available
window.TestRunner = TestRunner;
window.assert = assert;
window.MockStorage = MockStorage;