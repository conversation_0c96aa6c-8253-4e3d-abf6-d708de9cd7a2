// tests/core-tests.js - Core functionality tests

// Test Project Manager
function testProjectManager() {
  const mockStorage = new MockStorage();
  const projectManager = {
    storage: mockStorage,
    generateProjectId: () => 'test-project-id',
    generateProjectSlug: (name) => name.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
    validateProjectData: () => {}, // Mock validation
    getAllProjects: async () => await mockStorage.get('projects', [])
  };

  // Test project creation
  testRunner.registerTest('core', 'Project Creation', async () => {
    const projectData = {
      name: 'Test Project',
      description: 'A test project'
    };

    // Mock the createProject method
    const project = {
      id: 'test-project-id',
      name: 'Test Project',
      description: 'A test project',
      slug: 'test-project',
      personas: [],
      artifacts: [],
      status: 'active',
      priority: 'medium',
      tags: [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      version: 1,
      metadata: {}
    };

    await mockStorage.set('projects', [project]);

    const projects = await projectManager.getAllProjects();
    assert.equal(projects.length, 1);
    assert.equal(projects[0].name, 'Test Project');
    assert.equal(projects[0].status, 'active');
  });

  // Test project retrieval
  testRunner.registerTest('core', 'Project Retrieval', async () => {
    const projects = await projectManager.getAllProjects();
    assert.equal(projects.length, 1);

    const project = projects[0];
    assert.equal(project.id, 'test-project-id');
    assert.equal(project.name, 'Test Project');
    assert.equal(project.slug, 'test-project');
  });

  // Test project update
  testRunner.registerTest('core', 'Project Update', async () => {
    let projects = await projectManager.getAllProjects();
    const project = projects[0];

    project.name = 'Updated Project Name';
    project.updatedAt = new Date().toISOString();

    await mockStorage.set('projects', [project]);

    projects = await projectManager.getAllProjects();
    assert.equal(projects[0].name, 'Updated Project Name');
  });
}

// Test Persona Workflow
function testPersonaWorkflow() {
  const mockStorage = new MockStorage();
  const personaWorkflow = {
    storage: mockStorage,
    generatePersonaId: () => 'test-persona-id',
    generatePersonaSlug: (name) => name.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
    validatePersonaData: () => {}, // Mock validation
    getAllPersonas: async () => await mockStorage.get('personas', [])
  };

  // Test persona creation
  testRunner.registerTest('core', 'Persona Creation', async () => {
    const personaData = {
      name: 'Test Persona',
      role: 'assistant',
      systemPrompt: 'You are a helpful assistant.'
    };

    const persona = {
      id: 'test-persona-id',
      name: 'Test Persona',
      role: 'assistant',
      systemPrompt: 'You are a helpful assistant.',
      slug: 'test-persona',
      status: 'active',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      usageCount: 0
    };

    await mockStorage.set('personas', [persona]);

    const personas = await personaWorkflow.getAllPersonas();
    assert.equal(personas.length, 1);
    assert.equal(personas[0].name, 'Test Persona');
    assert.equal(personas[0].role, 'assistant');
  });

  // Test active persona
  testRunner.registerTest('core', 'Active Persona Management', async () => {
    await mockStorage.set('activePersona', 'test-persona-id');

    const activePersonaId = await mockStorage.get('activePersona');
    assert.equal(activePersonaId, 'test-persona-id');
  });
}

// Test Storage System
function testStorageSystem() {
  const storage = new MockStorage();

  testRunner.registerTest('core', 'Storage Set/Get', async () => {
    await storage.set('test-key', { name: 'Test Data' });
    const result = await storage.get('test-key');

    assert.equal(result.name, 'Test Data');
  });

  testRunner.registerTest('core', 'Storage Remove', async () => {
    await storage.set('test-key', 'test-value');
    await storage.remove('test-key');

    const result = await storage.get('test-key', 'default');
    assert.equal(result, 'default');
  });
}

// Test Utility Functions
function testUtilityFunctions() {
  // Test slug generation
  testRunner.registerTest('core', 'Slug Generation', () => {
    const slugGenerator = {
      generate: (text) => text.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '')
    };

    const result = slugGenerator.generate('Hello World!');
    assert.equal(result, 'hello-world');
  });

  // Test YAML processing
  testRunner.registerTest('core', 'YAML Processing', () => {
    const yamlProcessor = {
      parse: (content) => {
        const frontMatter = {};
        const yamlMatch = content.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/);
        if (yamlMatch) {
          // Simple parsing (would use proper YAML parser in production)
          return {
            frontMatter,
            content: yamlMatch[2]
          };
        }
        return { frontMatter, content };
      }
    };

    const testContent = `---
title: Test Document
---

# Hello World`;

    const result = yamlProcessor.parse(testContent);
    assert.equal(result.content.trim(), '# Hello World');
  });
}

// Test Export System
function testExportSystem() {
  const mockStorage = new MockStorage();
  const exportSystem = {
    storage: mockStorage,
    generateFileName: (name, ext) => `${name.toLowerCase().replace(/\s+/g, '-')}${ext}`,
    artifactEditor: {
      getArtifact: async () => ({
        id: 'test-artifact',
        name: 'Test Artifact',
        content: '# Test Content',
        projectId: 'test-project',
        personaId: 'test-persona'
      })
    },
    projectManager: {
      getProject: async () => ({
        id: 'test-project',
        name: 'Test Project'
      })
    },
    personaWorkflow: {
      getPersona: async () => ({
        id: 'test-persona',
        name: 'Test Persona'
      })
    }
  };

  testRunner.registerTest('core', 'Export File Name Generation', () => {
    const fileName = exportSystem.generateFileName('Test Document', '.md');
    assert.equal(fileName, 'test-document.md');
  });

  testRunner.registerTest('core', 'Export Data Structure', async () => {
    // Test that export creates proper data structure
    const exportData = {
      fileName: 'test-artifact.md',
      content: '# Test Content',
      contentType: 'text/markdown',
      size: 15,
      metadata: {
        artifactId: 'test-artifact',
        projectId: 'test-project',
        personaId: 'test-persona'
      }
    };

    assert.equal(exportData.fileName, 'test-artifact.md');
    assert.equal(exportData.contentType, 'text/markdown');
    assert.equal(exportData.metadata.artifactId, 'test-artifact');
  });
}

// Initialize all core tests
document.addEventListener('DOMContentLoaded', () => {
  testProjectManager();
  testPersonaWorkflow();
  testStorageSystem();
  testUtilityFunctions();
  testExportSystem();

  console.log('Core tests initialized');
});