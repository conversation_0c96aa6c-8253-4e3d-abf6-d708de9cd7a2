// tests/ui-tests.js - UI component tests

// Test DOM manipulation and UI interactions
function testUIComponents() {
  testRunner.registerTest('ui', 'DOM Element Creation', () => {
    // Test basic DOM manipulation
    const testElement = document.createElement('div');
    testElement.id = 'test-element';
    testElement.className = 'test-class';
    testElement.textContent = 'Test Content';

    assert.equal(testElement.id, 'test-element');
    assert.equal(testElement.className, 'test-class');
    assert.equal(testElement.textContent, 'Test Content');

    // Clean up
    if (testElement.parentNode) {
      testElement.parentNode.removeChild(testElement);
    }
  });

  testRunner.registerTest('ui', 'CSS Class Manipulation', () => {
    const testElement = document.createElement('div');
    testElement.classList.add('test-class');

    assert.equal(testElement.classList.contains('test-class'), true);

    testElement.classList.remove('test-class');
    assert.equal(testElement.classList.contains('test-class'), false);

    testElement.classList.add('class1', 'class2');
    assert.equal(testElement.classList.contains('class1'), true);
    assert.equal(testElement.classList.contains('class2'), true);
  });

  testRunner.registerTest('ui', 'Event Listener Functionality', () => {
    return new Promise((resolve) => {
      const testElement = document.createElement('button');
      let clicked = false;

      testElement.addEventListener('click', () => {
        clicked = true;
        resolve(true);
      });

      // Simulate click
      testElement.click();

      // If click doesn't work, resolve after timeout
      setTimeout(() => {
        if (!clicked) {
          resolve(false);
        }
      }, 100);
    });
  });
}

// Test sidebar functionality
function testSidebarFunctionality() {
  testRunner.registerTest('ui', 'Sidebar Tab Navigation', () => {
    // Test that tab elements exist
    const projectsTab = document.getElementById('projects-tab');
    const personasTab = document.getElementById('personas-tab');
    const artifactsTab = document.getElementById('artifacts-tab');
    const exportTab = document.getElementById('export-tab');

    assert.truthy(projectsTab);
    assert.truthy(personasTab);
    assert.truthy(artifactsTab);
    assert.truthy(exportTab);
  });

  testRunner.registerTest('ui', 'Tab Button States', () => {
    const tabButtons = document.querySelectorAll('.tab-button');
    assert.equal(tabButtons.length > 0, true);

    // Check that at least one tab button exists and has proper structure
    const firstButton = tabButtons[0];
    assert.truthy(firstButton.dataset.tab);
    assert.equal(typeof firstButton.textContent, 'string');
  });
}

// Test form validation
function testFormValidation() {
  testRunner.registerTest('ui', 'Form Input Validation', () => {
    const input = document.createElement('input');
    input.type = 'text';
    input.required = true;

    // Test empty required field
    assert.equal(input.checkValidity(), false);

    input.value = 'test value';
    assert.equal(input.checkValidity(), true);
  });

  testRunner.registerTest('ui', 'Email Input Validation', () => {
    const input = document.createElement('input');
    input.type = 'email';

    input.value = 'invalid-email';
    assert.equal(input.checkValidity(), false);

    input.value = '<EMAIL>';
    assert.equal(input.checkValidity(), true);
  });
}

// Test responsive design
function testResponsiveDesign() {
  testRunner.registerTest('ui', 'Responsive Breakpoints', () => {
    const testElement = document.createElement('div');
    testElement.style.width = '100%';

    // Test media query simulation
    const originalWidth = window.innerWidth;

    // Mock small screen
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 400
    });

    // Trigger resize event
    window.dispatchEvent(new Event('resize'));

    // Reset
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: originalWidth
    });

    assert.equal(window.innerWidth, originalWidth);
  });
}

// Test accessibility features
function testAccessibility() {
  testRunner.registerTest('ui', 'ARIA Labels', () => {
    const button = document.createElement('button');
    button.setAttribute('aria-label', 'Test Button');

    assert.equal(button.getAttribute('aria-label'), 'Test Button');
  });

  testRunner.registerTest('ui', 'Keyboard Navigation', () => {
    const button = document.createElement('button');
    button.tabIndex = 0;

    assert.equal(button.tabIndex, 0);

    button.focus();
    assert.equal(document.activeElement, button);
  });

  testRunner.registerTest('ui', 'Focus Management', () => {
    const input = document.createElement('input');
    const label = document.createElement('label');
    label.setAttribute('for', 'test-input');
    input.id = 'test-input';

    assert.equal(label.getAttribute('for'), 'test-input');
    assert.equal(input.id, 'test-input');
  });
}

// Test error handling in UI
function testUIErrorHandling() {
  testRunner.registerTest('ui', 'Error Message Display', () => {
    const errorElement = document.createElement('div');
    errorElement.className = 'error-message';
    errorElement.textContent = 'Test error message';

    assert.equal(errorElement.className, 'error-message');
    assert.equal(errorElement.textContent, 'Test error message');
    assert.equal(errorElement.style.display !== 'none', true);
  });

  testRunner.registerTest('ui', 'Loading State Management', () => {
    const loadingElement = document.createElement('div');
    loadingElement.className = 'loading';

    assert.equal(loadingElement.classList.contains('loading'), true);

    loadingElement.classList.remove('loading');
    assert.equal(loadingElement.classList.contains('loading'), false);
  });
}

// Test animation and transitions
function testAnimations() {
  testRunner.registerTest('ui', 'CSS Transition Support', () => {
    const element = document.createElement('div');
    element.style.transition = 'opacity 0.3s ease';

    const computedStyle = getComputedStyle(element);
    const transitionProperty = computedStyle.getPropertyValue('transition');

    assert.truthy(transitionProperty.includes('opacity') || transitionProperty.includes('all'));
  });

  testRunner.registerTest('ui', 'Animation Playback', () => {
    return new Promise((resolve) => {
      const element = document.createElement('div');
      element.style.animation = 'fadeIn 0.5s ease';

      // Add animation style
      const style = document.createElement('style');
      style.textContent = `
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
      `;
      document.head.appendChild(style);

      element.style.animation = 'fadeIn 0.5s ease';

      // Animation should complete within reasonable time
      setTimeout(() => {
        resolve(true);
      }, 600);
    });
  });
}

// Test localStorage integration
function testLocalStorageIntegration() {
  testRunner.registerTest('ui', 'localStorage Availability', () => {
    assert.truthy(typeof Storage !== 'undefined');
    assert.truthy(typeof localStorage !== 'undefined');
  });

  testRunner.registerTest('ui', 'localStorage Operations', () => {
    const testKey = 'test-ui-key';
    const testValue = 'test-ui-value';

    localStorage.setItem(testKey, testValue);
    const retrieved = localStorage.getItem(testKey);

    assert.equal(retrieved, testValue);

    localStorage.removeItem(testKey);
    const deleted = localStorage.getItem(testKey);

    assert.equal(deleted, null);
  });
}

// Test modal functionality
function testModalFunctionality() {
  testRunner.registerTest('ui', 'Modal Display Toggle', () => {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.style.display = 'none';

    assert.equal(modal.style.display, 'none');

    modal.style.display = 'flex';
    assert.equal(modal.style.display, 'flex');
  });

  testRunner.registerTest('ui', 'Modal Form Submission', () => {
    const form = document.createElement('form');
    const input = document.createElement('input');
    input.type = 'text';
    input.name = 'test-field';
    input.value = 'test value';

    form.appendChild(input);

    const formData = new FormData(form);
    const value = formData.get('test-field');

    assert.equal(value, 'test value');
  });
}

// Initialize all UI tests
document.addEventListener('DOMContentLoaded', () => {
  testUIComponents();
  testSidebarFunctionality();
  testFormValidation();
  testResponsiveDesign();
  testAccessibility();
  testUIErrorHandling();
  testAnimations();
  testLocalStorageIntegration();
  testModalFunctionality();

  console.log('UI tests initialized');
});