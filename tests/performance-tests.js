// tests/performance-tests.js - Performance testing for extension functionality

// Test storage performance
function testStoragePerformance() {
  testRunner.registerTest('performance', 'Storage Write Performance', async () => {
    const mockStorage = new MockStorage();
    const testData = { name: 'Performance Test', data: 'x'.repeat(1000) };

    const startTime = performance.now();

    // Perform 100 write operations
    for (let i = 0; i < 100; i++) {
      await mockStorage.set(`perf-test-${i}`, { ...testData, id: i });
    }

    const endTime = performance.now();
    const duration = endTime - startTime;

    console.log(`100 storage writes took ${duration.toFixed(2)}ms (${(duration/100).toFixed(2)}ms per write)`);

    // Should complete within reasonable time (less than 1000ms total)
    assert.equal(duration < 1000, true);
  });

  testRunner.registerTest('performance', 'Storage Read Performance', async () => {
    const mockStorage = new MockStorage();

    // Setup test data
    for (let i = 0; i < 50; i++) {
      await mockStorage.set(`read-test-${i}`, { id: i, data: 'x'.repeat(500) });
    }

    const startTime = performance.now();

    // Perform 50 read operations
    for (let i = 0; i < 50; i++) {
      await mockStorage.get(`read-test-${i}`);
    }

    const endTime = performance.now();
    const duration = endTime - startTime;

    console.log(`50 storage reads took ${duration.toFixed(2)}ms (${(duration/50).toFixed(2)}ms per read)`);

    // Should complete within reasonable time (less than 500ms total)
    assert.equal(duration < 500, true);
  });
}

// Test DOM manipulation performance
function testDOMPerformance() {
  testRunner.registerTest('performance', 'DOM Element Creation', () => {
    const startTime = performance.now();

    // Create 1000 DOM elements
    const fragment = document.createDocumentFragment();
    for (let i = 0; i < 1000; i++) {
      const div = document.createElement('div');
      div.className = `test-element-${i}`;
      div.textContent = `Element ${i}`;
      fragment.appendChild(div);
    }

    const endTime = performance.now();
    const duration = endTime - startTime;

    console.log(`Creating 1000 DOM elements took ${duration.toFixed(2)}ms (${(duration/1000).toFixed(4)}ms per element)`);

    // Should complete within reasonable time (less than 100ms total)
    assert.equal(duration < 100, true);
  });

  testRunner.registerTest('performance', 'DOM Query Performance', () => {
    // Setup test elements
    const container = document.createElement('div');
    for (let i = 0; i < 100; i++) {
      const div = document.createElement('div');
      div.className = 'query-test';
      div.setAttribute('data-id', `test-${i}`);
      container.appendChild(div);
    }
    document.body.appendChild(container);

    const startTime = performance.now();

    // Perform various queries
    const byClass = container.querySelectorAll('.query-test');
    const byAttribute = container.querySelectorAll('[data-id]');
    const byId = container.querySelector('[data-id="test-50"]');

    const endTime = performance.now();
    const duration = endTime - startTime;

    console.log(`DOM queries took ${duration.toFixed(2)}ms`);

    assert.equal(byClass.length, 100);
    assert.equal(byAttribute.length, 100);
    assert.truthy(byId);

    // Cleanup
    document.body.removeChild(container);
  });
}

// Test memory usage
function testMemoryUsage() {
  testRunner.registerTest('performance', 'Memory Leak Prevention', async () => {
    if (!performance.memory) {
      console.log('Memory API not available, skipping memory test');
      assert.equal(true, true);
      return;
    }

    const startMemory = performance.memory.usedJSHeapSize;

    // Create and destroy objects
    const objects = [];
    for (let i = 0; i < 1000; i++) {
      objects.push({
        id: `memory-test-${i}`,
        data: 'x'.repeat(1000)
      });
    }

    // Clear references
    objects.length = 0;

    // Force garbage collection if available
    if (typeof gc !== 'undefined') {
      gc();
    }

    // Small delay to allow GC
    await new Promise(resolve => setTimeout(resolve, 100));

    const endMemory = performance.memory.usedJSHeapSize;
    const memoryIncrease = endMemory - startMemory;

    console.log(`Memory increase: ${memoryIncrease} bytes (${(memoryIncrease/1024/1024).toFixed(2)} MB)`);

    // Memory increase should be reasonable (less than 10MB)
    assert.equal(memoryIncrease < 10 * 1024 * 1024, true);
  });
}

// Test event handling performance
function testEventHandlingPerformance() {
  testRunner.registerTest('performance', 'Event Handler Performance', () => {
    const button = document.createElement('button');
    let clickCount = 0;

    const startTime = performance.now();

    // Add event listener
    const clickHandler = () => {
      clickCount++;
    };

    button.addEventListener('click', clickHandler);

    // Simulate multiple clicks
    for (let i = 0; i < 1000; i++) {
      button.click();
    }

    const endTime = performance.now();
    const duration = endTime - startTime;

    console.log(`1000 event dispatches took ${duration.toFixed(2)}ms (${(duration/1000).toFixed(4)}ms per event)`);

    assert.equal(clickCount, 1000);

    // Cleanup
    button.removeEventListener('click', clickHandler);

    // Should complete within reasonable time (less than 100ms total)
    assert.equal(duration < 100, true);
  });
}

// Test string processing performance
function testStringProcessingPerformance() {
  testRunner.registerTest('performance', 'String Processing', () => {
    const testString = 'x'.repeat(10000); // 10KB string
    const startTime = performance.now();

    // Perform various string operations
    const upperCase = testString.toUpperCase();
    const lowerCase = upperCase.toLowerCase();
    const replaced = testString.replace(/x/g, 'y');
    const split = testString.split('x');
    const joined = split.join('z');

    const endTime = performance.now();
    const duration = endTime - startTime;

    console.log(`String processing took ${duration.toFixed(2)}ms`);

    assert.equal(replaced.includes('y'), true);
    assert.equal(joined.includes('z'), true);

    // Should complete within reasonable time (less than 50ms)
    assert.equal(duration < 50, true);
  });

  testRunner.registerTest('performance', 'Regex Performance', () => {
    const testString = 'test-string-with-hyphens-and-underscores'.repeat(1000);
    const startTime = performance.now();

    // Perform regex operations
    const hyphenated = testString.replace(/[^a-z0-9]+/g, '-');
    const clean = hyphenated.replace(/^-+|-+$/g, '');
    const words = clean.split('-');
    const filtered = words.filter(word => word.length > 2);

    const endTime = performance.now();
    const duration = endTime - startTime;

    console.log(`Regex processing took ${duration.toFixed(2)}ms`);

    assert.equal(filtered.length > 0, true);

    // Should complete within reasonable time (less than 100ms)
    assert.equal(duration < 100, true);
  });
}

// Test async operation performance
function testAsyncPerformance() {
  testRunner.registerTest('performance', 'Async Operation Performance', async () => {
    const startTime = performance.now();

    // Perform multiple async operations
    const promises = [];
    for (let i = 0; i < 50; i++) {
      promises.push(new Promise(resolve => {
        setTimeout(() => resolve(i), Math.random() * 10);
      }));
    }

    const results = await Promise.all(promises);
    const endTime = performance.now();
    const duration = endTime - startTime;

    console.log(`50 async operations took ${duration.toFixed(2)}ms (${(duration/50).toFixed(2)}ms per operation)`);

    assert.equal(results.length, 50);
    assert.equal(results[0], 0);
    assert.equal(results[49], 49);

    // Should complete within reasonable time (allowing for setTimeout delays)
    assert.equal(duration < 500, true);
  });
}

// Test CSS performance
function testCSSPerformance() {
  testRunner.registerTest('performance', 'CSS Style Recalculation', () => {
    // Create a large number of elements
    const container = document.createElement('div');
    for (let i = 0; i < 100; i++) {
      const div = document.createElement('div');
      div.className = 'css-test-element';
      div.textContent = `Element ${i}`;
      container.appendChild(div);
    }

    // Temporarily add to DOM for style calculation
    container.style.display = 'none';
    document.body.appendChild(container);

    const startTime = performance.now();

    // Apply styles that trigger recalculation
    const elements = container.querySelectorAll('.css-test-element');
    elements.forEach((element, index) => {
      element.style.color = index % 2 === 0 ? 'red' : 'blue';
      element.style.fontSize = `${12 + (index % 8)}px`;
      element.style.padding = `${index % 10}px`;
    });

    const endTime = performance.now();
    const duration = endTime - startTime;

    console.log(`CSS recalculation took ${duration.toFixed(2)}ms for 100 elements`);

    // Cleanup
    document.body.removeChild(container);

    // Should complete within reasonable time (less than 50ms)
    assert.equal(duration < 50, true);
  });
}

// Test localStorage performance
function testLocalStoragePerformance() {
  testRunner.registerTest('performance', 'localStorage Performance', () => {
    const startTime = performance.now();

    // Clear any existing test data
    for (let i = 0; i < 10; i++) {
      localStorage.removeItem(`perf-storage-${i}`);
    }

    // Perform storage operations
    for (let i = 0; i < 10; i++) {
      const data = {
        id: `perf-storage-${i}`,
        name: `Performance Test ${i}`,
        data: 'x'.repeat(1000) // 1KB of data
      };
      localStorage.setItem(`perf-storage-${i}`, JSON.stringify(data));
    }

    // Retrieve data
    for (let i = 0; i < 10; i++) {
      const data = localStorage.getItem(`perf-storage-${i}`);
      JSON.parse(data);
    }

    const endTime = performance.now();
    const duration = endTime - startTime;

    console.log(`localStorage operations took ${duration.toFixed(2)}ms (${(duration/20).toFixed(2)}ms per operation)`);

    // Cleanup
    for (let i = 0; i < 10; i++) {
      localStorage.removeItem(`perf-storage-${i}`);
    }

    // Should complete within reasonable time (less than 200ms)
    assert.equal(duration < 200, true);
  });
}

// Test JSON serialization performance
function testJSONPerformance() {
  testRunner.registerTest('performance', 'JSON Serialization', () => {
    const testData = {
      projects: [],
      personas: [],
      artifacts: []
    };

    // Create test data
    for (let i = 0; i < 100; i++) {
      testData.projects.push({
        id: `project-${i}`,
        name: `Project ${i}`,
        description: `Description ${i}`,
        artifacts: []
      });
    }

    const startTime = performance.now();

    // Perform serialization/deserialization
    for (let i = 0; i < 10; i++) {
      const serialized = JSON.stringify(testData);
      const deserialized = JSON.parse(serialized);
      assert.equal(deserialized.projects.length, 100);
    }

    const endTime = performance.now();
    const duration = endTime - startTime;

    console.log(`JSON operations took ${duration.toFixed(2)}ms (${(duration/10).toFixed(2)}ms per cycle)`);

    // Should complete within reasonable time (less than 50ms)
    assert.equal(duration < 50, true);
  });
}

// Initialize all performance tests
document.addEventListener('DOMContentLoaded', () => {
  testStoragePerformance();
  testDOMPerformance();
  testMemoryUsage();
  testEventHandlingPerformance();
  testStringProcessingPerformance();
  testAsyncPerformance();
  testCSSPerformance();
  testLocalStoragePerformance();
  testJSONPerformance();

  console.log('Performance tests initialized');
});